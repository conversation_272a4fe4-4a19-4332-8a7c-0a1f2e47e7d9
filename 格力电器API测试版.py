# coding:gbk
"""
格力电器API测试版
专门测试get_history_data API的正确调用方式
修复参数数量错误
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数 - 测试API调用"""
    
    print("🧪 格力电器API测试版")
    print("🔍 测试get_history_data API调用")
    print("🛠️ 修复参数数量错误")
    
    # 执行测试
    test_api_call(ContextInfo)

def test_api_call(ContextInfo):
    """测试API调用"""
    
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    
    print(f"\n📋 测试参数:")
    print(f"   股票: {stock_code}")
    print(f"   开始: {start_date}")
    print(f"   结束: {end_date}")
    
    try:
        print(f"\n🔄 测试1: 基本调用 (4个参数)")
        data1 = ContextInfo.get_history_data(stock_code, '5m', start_date, end_date)
        
        if data1 is not None:
            print(f"✅ 基本调用成功，获取 {len(data1)} 条数据")
            print(f"📊 数据类型: {type(data1)}")
            if hasattr(data1, 'columns'):
                print(f"📋 字段: {list(data1.columns)}")
            
            # 显示前几条数据
            print(f"\n📋 数据预览:")
            print(data1.head())
            
            # 保存测试结果
            save_test_result(data1, stock_code, start_date, end_date)
            
        else:
            print(f"❌ 基本调用返回None")
            
    except Exception as e:
        print(f"❌ 测试1失败: {str(e)}")
        
        # 尝试其他参数组合
        try:
            print(f"\n🔄 测试2: 尝试3个参数")
            data2 = ContextInfo.get_history_data(stock_code, '5m', start_date)
            print(f"✅ 测试2成功: {type(data2)}")
            if data2 is not None and len(data2) > 0:
                save_test_result(data2, stock_code, start_date, end_date)
        except Exception as e2:
            print(f"❌ 测试2失败: {str(e2)}")
            
        try:
            print(f"\n🔄 测试3: 尝试2个参数")
            data3 = ContextInfo.get_history_data(stock_code, '5m')
            print(f"✅ 测试3成功: {type(data3)}")
            if data3 is not None and len(data3) > 0:
                save_test_result(data3, stock_code, start_date, end_date)
        except Exception as e3:
            print(f"❌ 测试3失败: {str(e3)}")
            
        # 尝试使用get_market_data_ex作为备选
        try:
            print(f"\n🔄 测试4: 尝试get_market_data_ex")
            data4 = ContextInfo.get_market_data_ex([stock_code], '5m', start_date, end_date)
            print(f"✅ 测试4成功: {type(data4)}")
            if data4 is not None:
                print(f"📊 get_market_data_ex返回: {data4}")
        except Exception as e4:
            print(f"❌ 测试4失败: {str(e4)}")

def save_test_result(data, stock_code, start_date, end_date):
    """保存测试结果"""
    
    try:
        print(f"\n💾 开始保存测试结果...")
        
        # 检查数据结构
        print(f"📊 数据信息:")
        print(f"   类型: {type(data)}")
        print(f"   长度: {len(data) if data is not None else 0}")
        
        if hasattr(data, 'index'):
            print(f"   索引类型: {type(data.index)}")
            if len(data) > 0:
                print(f"   首条索引: {data.index[0]}")
                print(f"   末条索引: {data.index[-1]}")
        
        if hasattr(data, 'columns'):
            print(f"   列名: {list(data.columns)}")
        
        # 处理目标时间点
        target_times = ["0935", "0940", "0945"]
        results = []
        
        # 获取所有交易日
        all_dates = set()
        for index in data.index:
            all_dates.add(index[:8])
        
        sorted_dates = sorted(all_dates)
        print(f"\n📅 发现 {len(sorted_dates)} 个交易日")
        
        # 处理前3个交易日作为测试
        for date_str in sorted_dates[:3]:
            day_data = data[data.index.str.startswith(date_str)]
            
            if not day_data.empty:
                print(f"📅 {date_str}: {len(day_data)} 条数据")
                
                for target_time in target_times:
                    found_data = find_time_data(day_data, target_time)
                    
                    if found_data is not None:
                        result = {
                            '股票代码': stock_code,
                            '股票名称': '格力电器',
                            '日期': date_str,
                            '目标时间': target_time,
                            '实际时间': found_data.name,
                            '收盘价': round(found_data['close'], 2) if 'close' in found_data else 0
                        }
                        results.append(result)
                        print(f"  ✅ {target_time}: {result['收盘价']:.2f}元")
        
        if results:
            # 保存结果
            df = pd.DataFrame(results)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"格力电器_API测试_{timestamp}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"\n💾 测试结果已保存: {filename}")
            print(f"📊 测试记录: {len(df)} 条")
        else:
            print(f"\n❌ 未找到任何目标时间点数据")
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {str(e)}")

def find_time_data(day_data, target_time):
    """查找指定时间的数据"""
    
    patterns = []
    
    if target_time == "0935":
        patterns = ["0935", "0930"]
    elif target_time == "0940":
        patterns = ["0940"]
    elif target_time == "0945":
        patterns = ["0945"]
    else:
        patterns = [target_time]
    
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 测试说明
"""
🧪 API测试版本说明:

🎯 目的:
- 测试get_history_data API的正确调用方式
- 确定正确的参数格式和数量
- 验证返回数据的结构
- 修复"参数数量错误"问题

🔍 测试策略:
1. 测试4个参数: stock_code, period, start_date, end_date
2. 测试3个参数: stock_code, period, start_date
3. 测试2个参数: stock_code, period
4. 备选方案: get_market_data_ex

📊 输出内容:
- 详细的API调用结果
- 数据类型和结构分析
- 错误信息和解决方案
- 成功时的CSV测试文件

💡 使用方法:
1. 运行此测试版本
2. 查看详细的输出日志
3. 确定哪种参数组合有效
4. 根据结果修改正式版本

🔧 错误诊断:
- 参数数量和类型检查
- 多种API调用方式测试
- 详细的异常信息输出
- 数据结构完整分析
"""
