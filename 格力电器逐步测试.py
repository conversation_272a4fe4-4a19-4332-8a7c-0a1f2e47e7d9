# coding:gbk
"""
格力电器逐步测试
逐步测试不同的API调用方式，找到正确的参数组合
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数"""
    
    print("=" * 60)
    print("🔍 格力电器逐步测试")
    print("🎯 找到正确的API调用方式")
    print("=" * 60)
    
    stock_code = "000651.SZ"
    
    # 测试1: 最基本的调用
    test_basic_call(ContextInfo, stock_code)

def test_basic_call(ContextInfo, stock_code):
    """测试基本API调用"""
    
    print(f"\n📊 股票代码: {stock_code}")
    
    # 测试方案1: 只有股票代码和周期
    try:
        print(f"\n🔄 测试1: get_history_data(股票, 周期)")
        data1 = ContextInfo.get_history_data(stock_code, '5m')
        print(f"✅ 测试1成功: {type(data1)}, 长度: {len(data1) if data1 is not None else 0}")
        
        if data1 is not None and len(data1) > 0:
            print(f"📋 字段: {list(data1.columns) if hasattr(data1, 'columns') else '无columns'}")
            print(f"📅 时间范围: {data1.index[0]} 到 {data1.index[-1]}")
            
            # 如果成功，直接处理数据
            process_simple_data(data1, stock_code)
            return True
            
    except Exception as e:
        print(f"❌ 测试1失败: {str(e)}")
    
    # 测试方案2: 添加字段参数
    try:
        print(f"\n🔄 测试2: get_history_data(股票, 周期, 字段)")
        fields = ['close']  # 只要收盘价
        data2 = ContextInfo.get_history_data(stock_code, '5m', fields)
        print(f"✅ 测试2成功: {type(data2)}, 长度: {len(data2) if data2 is not None else 0}")
        
        if data2 is not None and len(data2) > 0:
            process_simple_data(data2, stock_code)
            return True
            
    except Exception as e:
        print(f"❌ 测试2失败: {str(e)}")
    
    # 测试方案3: 添加开始日期
    try:
        print(f"\n🔄 测试3: get_history_data(股票, 周期, 开始日期)")
        data3 = ContextInfo.get_history_data(stock_code, '5m', '20250701')
        print(f"✅ 测试3成功: {type(data3)}, 长度: {len(data3) if data3 is not None else 0}")
        
        if data3 is not None and len(data3) > 0:
            process_simple_data(data3, stock_code)
            return True
            
    except Exception as e:
        print(f"❌ 测试3失败: {str(e)}")
    
    # 测试方案4: 完整参数
    try:
        print(f"\n🔄 测试4: get_history_data(股票, 周期, 开始, 结束)")
        data4 = ContextInfo.get_history_data(stock_code, '5m', '20250701', '20250731')
        print(f"✅ 测试4成功: {type(data4)}, 长度: {len(data4) if data4 is not None else 0}")
        
        if data4 is not None and len(data4) > 0:
            process_simple_data(data4, stock_code)
            return True
            
    except Exception as e:
        print(f"❌ 测试4失败: {str(e)}")
    
    # 测试方案5: 使用get_market_data_ex
    try:
        print(f"\n🔄 测试5: get_market_data_ex")
        data5 = ContextInfo.get_market_data_ex([stock_code], '5m', '20250701', '20250731')
        print(f"✅ 测试5成功: {type(data5)}")
        
        if data5 is not None:
            print(f"📊 get_market_data_ex结果: {data5}")
            return True
            
    except Exception as e:
        print(f"❌ 测试5失败: {str(e)}")
    
    # 测试方案6: 尝试其他API
    try:
        print(f"\n🔄 测试6: 检查可用的API方法")
        
        # 列出ContextInfo的所有方法
        methods = [method for method in dir(ContextInfo) if 'data' in method.lower() or 'history' in method.lower()]
        print(f"📋 可用的数据相关方法: {methods}")
        
    except Exception as e:
        print(f"❌ 测试6失败: {str(e)}")
    
    print(f"\n❌ 所有测试都失败了")
    return False

def process_simple_data(data, stock_code):
    """处理简单数据"""
    
    print(f"\n🎉 开始处理数据...")
    
    try:
        # 查找目标时间点
        target_times = ["0935", "0940", "0945"]
        results = []
        
        # 获取最近几天的数据作为示例
        recent_dates = set()
        for index in data.index[-100:]:  # 只看最后100条数据
            recent_dates.add(index[:8])
        
        sorted_dates = sorted(recent_dates)[-5:]  # 最近5个交易日
        
        print(f"📅 处理最近 {len(sorted_dates)} 个交易日:")
        
        for date_str in sorted_dates:
            day_data = data[data.index.str.startswith(date_str)]
            
            if day_data.empty:
                continue
            
            print(f"  📅 {date_str}: {len(day_data)} 条数据", end=" -> ")
            
            day_results = []
            for target_time in target_times:
                # 查找目标时间
                matches = day_data[day_data.index.str.contains(target_time)]
                
                if not matches.empty:
                    found_data = matches.iloc[0]
                    
                    # 获取价格（尝试不同的字段名）
                    price = None
                    if 'close' in found_data:
                        price = found_data['close']
                    elif 'Close' in found_data:
                        price = found_data['Close']
                    elif len(found_data) > 0:
                        price = found_data.iloc[0]  # 如果只有一个值
                    
                    if price is not None:
                        result = {
                            '股票代码': stock_code,
                            '股票名称': '格力电器',
                            '日期': date_str,
                            '目标时间': target_time,
                            '实际时间': found_data.name,
                            '价格': round(float(price), 2)
                        }
                        results.append(result)
                        day_results.append(f"{target_time}:{price:.2f}")
            
            if day_results:
                print(f"✅ {', '.join(day_results)}")
            else:
                print(f"❌ 无数据")
        
        # 保存结果
        if results:
            df = pd.DataFrame(results)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"格力电器_逐步测试_{timestamp}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"\n💾 测试结果已保存: {filename}")
            print(f"📊 成功记录: {len(results)} 条")
            
            # 显示前几条结果
            print(f"\n📋 结果样例:")
            for i, result in enumerate(results[:3], 1):
                print(f"  {i}. {result['日期']} {result['目标时间']}: {result['价格']:.2f}元")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理失败: {str(e)}")
        return False

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 测试说明
"""
🔍 逐步测试说明:

🎯 目的:
- 逐一测试不同的API调用方式
- 找到在您的QMT环境中有效的方法
- 简化数据处理，专注于API调用

🧪 测试方案:
1. get_history_data(股票, 周期)
2. get_history_data(股票, 周期, 字段)
3. get_history_data(股票, 周期, 开始日期)
4. get_history_data(股票, 周期, 开始, 结束)
5. get_market_data_ex 备选方案
6. 列出可用的API方法

📊 处理策略:
- 只处理最近几天的数据
- 简化字段处理
- 重点验证API调用是否成功
- 生成简单的测试CSV文件

💡 使用方法:
1. 运行此测试版本
2. 查看哪个测试成功
3. 根据成功的方案调整最终版本
"""
