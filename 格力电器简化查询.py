# coding:gbk
"""
简化版：获取格力电器指定时间点股价
目标时间：9:35, 9:40, 9:45
注意：由于20250730是未来日期，代码会自动获取最近的交易日数据进行演示
"""

def init(ContextInfo):
    """初始化"""
    print("格力电器股价查询策略启动")
    print("目标查询时间：9:35, 9:40, 9:45")
    print("注意：由于20250730是未来日期，将获取最近交易日数据演示")

def handlebar(ContextInfo):
    """主执行函数"""

    # 格力电器股票代码
    stock_code = "000651.SZ"
    target_date = "20250730"  # 目标日期（未来）

    print(f"\n正在查询 {stock_code} 的股价数据...")

    try:
        # 首先尝试获取目标日期数据
        print(f"\n=== 尝试获取 {target_date} 的数据 ===")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=target_date,
            end_time=target_date,
            count=-1,
            subscribe=False
        )

        # 检查是否获取到目标日期数据
        target_data_found = False
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            if len(df) > 0:
                target_data_found = True
                print(f"成功获取目标日期 {len(df)} 条分钟数据")
                process_minute_data(df, target_date)

        if not target_data_found:
            print(f"未获取到 {target_date} 的数据（该日期为未来日期）")
            print("\n=== 获取最近交易日数据进行演示 ===")

            # 获取最近的分钟数据
            recent_minute_data = ContextInfo.get_market_data_ex(
                fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
                stock_code=[stock_code],
                period='1m',
                count=300,  # 获取最近300条分钟数据（约2个交易日）
                subscribe=False
            )

            if stock_code in recent_minute_data and not recent_minute_data[stock_code].empty:
                df_recent = recent_minute_data[stock_code]
                print(f"获取到最近 {len(df_recent)} 条分钟数据")

                # 找到最近的交易日
                latest_date = df_recent.index[-1][:8]  # 提取日期部分
                print(f"最近交易日: {latest_date}")

                # 筛选最近交易日的数据
                latest_day_data = df_recent[df_recent.index.str.startswith(latest_date)]
                if not latest_day_data.empty:
                    process_minute_data(latest_day_data, latest_date)
                else:
                    print("未找到最近交易日的分钟数据")
            else:
                print("未获取到任何分钟数据")

        # 获取最新可用数据作为参考
        print("\n=== 获取最新日线数据作为参考 ===")
        latest_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1d',
            count=5,  # 获取最近5天
            subscribe=False
        )

        if stock_code in latest_data and not latest_data[stock_code].empty:
            df_latest = latest_data[stock_code]
            print("格力电器最近交易日数据：")
            for idx, row in df_latest.tail(3).iterrows():
                print(f"{idx}: 收盘价 {row['close']:.2f} 元")

    except Exception as e:
        print(f"查询出错: {str(e)}")
        print("\n可能的解决方案：")
        print("1. 确保QMT已连接行情服务器")
        print("2. 下载格力电器的历史数据")
        print("3. 检查网络连接")

def process_minute_data(df, date_str):
    """处理分钟数据，查找指定时间点"""

    # 查找目标时间点
    target_times = ["0935", "0940", "0945"]  # 简化为HHMM格式

    print(f"\n格力电器 {date_str} 指定时间点股价：")
    print("=" * 60)

    found_any_data = False

    for time_str in target_times:
        # 查找匹配的时间 - 改进匹配逻辑
        matching_data = None

        # 尝试多种匹配方式
        for pattern in [time_str, f"{time_str}00", f"{time_str}30"]:
            temp_data = df[df.index.str.contains(pattern)]
            if not temp_data.empty:
                matching_data = temp_data
                break

        if matching_data is not None and not matching_data.empty:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]
            found_any_data = True

            print(f"时间: {time_str[:2]}:{time_str[2:]} (实际: {actual_time})")
            print(f"  股价: {row['close']:.2f} 元")
            print(f"  开盘: {row['open']:.2f} 元")
            print(f"  最高: {row['high']:.2f} 元")
            print(f"  最低: {row['low']:.2f} 元")
            print(f"  成交量: {row['volume']:,.0f} 股")
            print(f"  成交额: {row['amount']:,.0f} 元")
            print("-" * 30)
        else:
            print(f"时间: {time_str[:2]}:{time_str[2:]} - 无数据")

    if not found_any_data:
        print("未找到指定时间点的数据")
        print("\n可用的时间点示例：")
        print(df.head(10).index.tolist())
            
        # 方法2：获取最新可用数据作为参考
        print("\n=== 获取最新数据作为参考 ===")
        latest_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1d',
            count=5,  # 获取最近5天
            subscribe=False
        )
        
        if stock_code in latest_data and not latest_data[stock_code].empty:
            df_latest = latest_data[stock_code]
            print("格力电器最近交易日数据：")
            for idx, row in df_latest.tail(3).iterrows():
                print(f"{idx}: 收盘价 {row['close']:.2f} 元")
                
    except Exception as e:
        print(f"查询出错: {str(e)}")
        print("\n可能的解决方案：")
        print("1. 确保QMT已连接行情服务器")
        print("2. 下载格力电器的历史数据")
        print("3. 检查网络连接")

# 数据下载函数（可选使用）
def download_data():
    """下载历史数据"""
    try:
        download_history_data("000651.SZ", "1m", "20250730", "20250730")
        print("数据下载完成")
    except:
        print("数据下载失败，请手动在QMT中下载")

"""
使用说明：

1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 点击"运行"执行查询

注意事项：
- 000651.SZ 是格力电器的股票代码
- 2025年7月30日是未来日期，需要等到该日期才有实际数据
- 如果查询历史日期，需要先在QMT中下载相应的历史数据
- 交易时间：9:30-11:30, 13:00-15:00

如果没有数据，请：
1. 在QMT菜单中选择"数据管理"
2. 选择"股票数据下载"
3. 输入000651.SZ，选择1分钟数据
4. 设置时间范围包含目标日期
5. 点击下载
"""
