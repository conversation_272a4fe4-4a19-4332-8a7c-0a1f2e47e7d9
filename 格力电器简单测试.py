# coding:gbk
"""
格力电器简单测试
最简单的测试版本，确保能看到输出
"""

def init(ContextInfo):
    """初始化函数"""
    print("=" * 50)
    print("🧪 格力电器简单测试开始")
    print("=" * 50)
    
    # 基本信息测试
    print("✅ init函数执行成功")
    print("📊 ContextInfo对象可用")
    
    # 测试基本API
    test_basic_api(ContextInfo)

def test_basic_api(ContextInfo):
    """测试基本API"""
    
    stock_code = "000651.SZ"
    
    print(f"\n🔄 开始API测试...")
    print(f"📊 股票代码: {stock_code}")
    
    try:
        # 测试1: 最简单的调用
        print(f"\n🔄 测试1: get_history_data 最简调用")
        data1 = ContextInfo.get_history_data(stock_code, '5m')
        print(f"✅ 测试1成功: {type(data1)}")
        
        if data1 is not None:
            print(f"📊 数据长度: {len(data1)}")
            print(f"📋 数据类型: {type(data1)}")
            
            if hasattr(data1, 'columns'):
                print(f"📋 列名: {list(data1.columns)}")
            
            if len(data1) > 0:
                print(f"📅 首条数据: {data1.index[0] if hasattr(data1, 'index') else '无索引'}")
                print(f"📅 末条数据: {data1.index[-1] if hasattr(data1, 'index') else '无索引'}")
                
                # 显示前3条数据
                print(f"\n📋 前3条数据:")
                print(data1.head(3))
                
                print(f"\n🎉 测试1完全成功!")
                return True
        else:
            print(f"❌ 测试1返回None")
            
    except Exception as e:
        print(f"❌ 测试1失败: {str(e)}")
    
    try:
        # 测试2: 带日期的调用
        print(f"\n🔄 测试2: 带开始日期")
        data2 = ContextInfo.get_history_data(stock_code, '5m', '20250701')
        print(f"✅ 测试2成功: {type(data2)}")
        
        if data2 is not None and len(data2) > 0:
            print(f"📊 数据长度: {len(data2)}")
            print(f"🎉 测试2完全成功!")
            return True
            
    except Exception as e:
        print(f"❌ 测试2失败: {str(e)}")
    
    try:
        # 测试3: 完整日期范围
        print(f"\n🔄 测试3: 完整日期范围")
        data3 = ContextInfo.get_history_data(stock_code, '5m', '20250701', '20250731')
        print(f"✅ 测试3成功: {type(data3)}")
        
        if data3 is not None and len(data3) > 0:
            print(f"📊 数据长度: {len(data3)}")
            print(f"🎉 测试3完全成功!")
            return True
            
    except Exception as e:
        print(f"❌ 测试3失败: {str(e)}")
    
    # 测试备选方案
    try:
        print(f"\n🔄 测试4: get_market_data_ex备选")
        data4 = ContextInfo.get_market_data_ex([stock_code], '5m', '20250701', '20250731')
        print(f"✅ 测试4成功: {type(data4)}")
        
        if data4 is not None:
            print(f"📊 get_market_data_ex结果: {data4}")
            print(f"🎉 测试4完全成功!")
            return True
            
    except Exception as e:
        print(f"❌ 测试4失败: {str(e)}")
    
    print(f"\n❌ 所有测试都失败了")
    print(f"💡 可能的原因:")
    print(f"   1. 网络连接问题")
    print(f"   2. QMT服务未启动")
    print(f"   3. API版本不兼容")
    print(f"   4. 股票代码错误")
    
    return False

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 使用说明
"""
🧪 简单测试版说明:

🎯 目的:
- 确保能看到输出结果
- 测试最基本的API调用
- 逐步增加参数复杂度
- 快速诊断问题

📊 测试内容:
1. 最简调用: get_history_data(code, period)
2. 带日期: get_history_data(code, period, start)
3. 完整范围: get_history_data(code, period, start, end)
4. 备选方案: get_market_data_ex

💡 使用方法:
1. 运行此策略
2. 查看输出窗口
3. 确认哪个测试成功
4. 根据结果调整正式版本

🔍 输出位置:
- 查看QMT下方的"日志输出"窗口
- 或者"输出"、"控制台"窗口
- 确保能看到完整的测试结果
"""
