# coding:gbk
"""
格力电器安全执行版
使用多重保护机制，确保只执行一次，避免重复和无限循环
"""

import pandas as pd
from datetime import datetime
import time

def init(ContextInfo):
    """初始化"""
    print("🔒 格力电器安全执行版")
    print("🛡️ 多重保护，确保只执行一次")
    
    # 初始化执行控制变量
    ContextInfo.execution_time = None
    ContextInfo.execution_completed = False
    ContextInfo.execution_count = 0

def handlebar(ContextInfo):
    """主执行函数 - 带安全保护"""
    
    # 第一重保护：执行次数限制
    ContextInfo.execution_count += 1
    if ContextInfo.execution_count > 1:
        return
    
    # 第二重保护：执行完成标志
    if ContextInfo.execution_completed:
        return
    
    # 第三重保护：时间间隔检查
    current_time = time.time()
    if ContextInfo.execution_time is not None:
        if current_time - ContextInfo.execution_time < 60:  # 60秒内不重复执行
            return
    
    # 第四重保护：只在最后一根K线执行
    if not ContextInfo.is_last_bar():
        return
    
    print(f"\n🚀 开始执行数据获取任务...")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 记录执行时间
    ContextInfo.execution_time = current_time
    
    try:
        # 执行主要任务
        success = execute_data_task(ContextInfo)
        
        if success:
            print("✅ 任务执行成功")
            ContextInfo.execution_completed = True
        else:
            print("❌ 任务执行失败")
            
    except Exception as e:
        print(f"❌ 执行异常: {str(e)}")
    
    print("🔒 执行完毕，策略已锁定，不会再次执行")

def execute_data_task(ContextInfo):
    """执行数据获取任务"""
    
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"📊 股票: {stock_code} (格力电器)")
    print(f"📅 时间范围: {start_date} - {end_date}")
    print(f"🎯 目标时间: {', '.join(target_times)}")
    
    try:
        # 获取本地数据
        print("\n📡 正在获取本地数据...")
        
        data = ContextInfo.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if stock_code not in data or data[stock_code].empty:
            print("❌ 本地数据为空")
            print("💡 解决方案:")
            print("   1. 打开QMT -> 操作 -> 数据管理")
            print("   2. 选择股票: 000651.SZ")
            print("   3. 选择周期: 1分钟")
            print("   4. 选择时间: 20250430-20250731")
            print("   5. 点击下载数据")
            return False
        
        df = data[stock_code]
        print(f"✅ 成功获取 {len(df):,} 条数据")
        
        # 显示数据概况
        first_date = df.index[0][:8]
        last_date = df.index[-1][:8]
        print(f"📅 实际数据范围: {first_date} - {last_date}")
        
        # 提取目标数据
        results = process_target_times(df, stock_code, target_times)
        
        if not results:
            print("❌ 未提取到任何目标时间点数据")
            return False
        
        # 保存数据
        filename = save_results(results, start_date, end_date)
        
        if filename:
            print(f"📁 数据已保存: {filename}")
            print(f"🎯 提取记录: {len(results)} 条")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 数据处理错误: {str(e)}")
        return False

def process_target_times(df, stock_code, target_times):
    """处理目标时间点数据"""
    
    results = []
    processed_dates = set()
    
    print(f"\n📊 开始处理目标时间点...")
    
    # 获取所有唯一日期
    all_dates = set()
    for index in df.index:
        all_dates.add(index[:8])
    
    sorted_dates = sorted(all_dates)
    print(f"📅 发现 {len(sorted_dates)} 个交易日")
    
    for date_str in sorted_dates:
        # 获取当日数据
        day_data = df[df.index.str.startswith(date_str)]
        
        if day_data.empty:
            continue
        
        print(f"📅 {date_str}: {len(day_data)} 条数据", end=" -> ")
        
        day_results = []
        for target_time in target_times:
            found_data = find_closest_time(day_data, target_time)
            
            if found_data is not None:
                result = {
                    '股票代码': stock_code,
                    '股票名称': '格力电器',
                    '日期': date_str,
                    '目标时间': target_time,
                    '实际时间': found_data.name,
                    '开盘价': round(found_data['open'], 2),
                    '最高价': round(found_data['high'], 2),
                    '最低价': round(found_data['low'], 2),
                    '收盘价': round(found_data['close'], 2),
                    '成交量': int(found_data['volume']),
                    '成交额': round(found_data['amount'], 2)
                }
                results.append(result)
                day_results.append(f"{target_time}:{result['收盘价']:.2f}")
        
        if day_results:
            print(f"✅ {', '.join(day_results)}")
        else:
            print("❌ 无数据")
    
    return results

def find_closest_time(day_data, target_time):
    """查找最接近的时间数据"""
    
    # 按优先级尝试匹配
    search_patterns = [
        target_time + "00",  # 精确匹配，如 093500
        target_time + "30",  # 半分钟匹配，如 093530
        target_time,         # 部分匹配，如 0935
        target_time[:3]      # 小时匹配，如 093
    ]
    
    for pattern in search_patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def save_results(results, start_date, end_date):
    """保存结果到CSV"""
    
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_安全版_{start_date}到{end_date}_{timestamp}.csv"
        
        # 保存文件
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 显示保存信息
        print(f"\n💾 保存信息:")
        print(f"   文件名: {filename}")
        print(f"   总记录: {len(df)} 条")
        print(f"   交易日: {df['日期'].nunique()} 天")
        print(f"   时间点: {len(df['目标时间'].unique())} 个")
        
        # 显示价格范围
        if '收盘价' in df.columns:
            min_price = df['收盘价'].min()
            max_price = df['收盘价'].max()
            avg_price = df['收盘价'].mean()
            print(f"   价格范围: {min_price:.2f} - {max_price:.2f} 元")
            print(f"   平均价格: {avg_price:.2f} 元")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

# 安全机制说明
"""
🔒 安全执行版特点：

🛡️ 四重保护机制：
1. 执行次数限制 - 最多执行1次
2. 完成标志检查 - 执行完成后不再执行
3. 时间间隔控制 - 60秒内不重复执行
4. K线位置检查 - 只在最后一根K线执行

✅ 解决的问题：
- 防止无限循环
- 避免重复生成CSV文件
- 停止终端无限滚动
- 确保数据完整性

🎯 使用方法：
1. 确保本地数据已下载
2. 运行此策略
3. 等待执行完成
4. 查看生成的CSV文件

💡 如果需要重新执行：
- 重新启动策略即可
- 每次启动只执行一次
"""
