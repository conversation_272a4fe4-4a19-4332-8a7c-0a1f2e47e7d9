# coding:gbk
"""
格力电器历史数据查询版
获取2025年4月30日到2025年8月4日期间每个交易日的 9:35、9:40、9:45 三个时间点的股价
保存所有数据到CSV文件
"""

import pandas as pd
from datetime import datetime, timedelta

def init(ContextInfo):
    """初始化"""
    print("=" * 60)
    print("格力电器历史数据查询")
    print("时间范围：2025年4月30日 到 2025年8月4日")
    print("目标时间点：9:35、9:40、9:45")
    print("获取期间内所有交易日的目标时间点股价数据")
    print("=" * 60)

def handlebar(ContextInfo):
    """主执行函数"""

    stock_code = "000651.SZ"  # 格力电器
    start_date = "20250430"   # 开始日期：2025年4月30日
    end_date = "20250804"     # 结束日期：2025年8月4日
    target_times = ["0935", "0940", "0945"]  # 目标时间点

    print(f"\n正在查询 {stock_code} 从 {start_date} 到 {end_date} 的股价数据...")

    try:
        # 获取指定时间范围的分钟数据
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            subscribe=False
        )

        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            print(f"✅ 成功获取 {len(df)} 条分钟数据")

            # 获取数据的日期范围
            first_date = df.index[0][:8]
            last_date = df.index[-1][:8]
            print(f"📅 数据时间范围: {first_date} 到 {last_date}")

            # 按日期分组处理数据
            all_target_data = []
            processed_dates = set()

            print(f"\n开始处理各交易日的目标时间点数据...")
            print("=" * 70)

            for index in df.index:
                current_date = index[:8]  # 提取日期 YYYYMMDD

                # 如果这个日期还没处理过
                if current_date not in processed_dates:
                    processed_dates.add(current_date)

                    # 获取当天的数据
                    day_data = df[df.index.str.startswith(current_date)]

                    if not day_data.empty:
                        print(f"\n📅 处理日期: {current_date} ({len(day_data)} 条分钟数据)")

                        # 查找当天的目标时间点
                        day_target_data = process_day_data(day_data, current_date, target_times, stock_code)
                        all_target_data.extend(day_target_data)

            # 保存所有数据到CSV文件
            if all_target_data:
                filename = save_all_target_data_csv(all_target_data, start_date, end_date)

                print(f"\n" + "=" * 70)
                print(f"✅ 数据处理完成！")
                print(f"📊 总共获取 {len(all_target_data)} 个时间点的数据")
                print(f"📁 文件名: {filename}")
                print(f"📅 涵盖 {len(processed_dates)} 个交易日")
                print("=" * 70)

                # 显示统计信息
                show_summary_statistics(all_target_data)

            else:
                print(f"\n❌ 未找到任何目标时间点的数据")
                print("可能原因：")
                print("1. 指定时间范围内没有交易数据")
                print("2. 数据格式不匹配")
                print("3. 时间范围包含非交易日")

        else:
            print(f"\n❌ 未获取到指定时间范围的数据")
            print("可能原因：")
            print("1. 时间范围包含未来日期")
            print("2. 时间范围内都是非交易日")
            print("3. 需要先在QMT中下载历史数据")
            print("4. 网络连接问题")

            print(f"\n💡 解决方案：")
            print("1. 检查日期范围是否正确")
            print("2. 在QMT中下载000651.SZ的1分钟历史数据")
            print("3. 确保数据时间范围包含20250430-20250804")
            print("4. 检查网络连接和QMT登录状态")

    except Exception as e:
        print(f"❌ 查询出错: {str(e)}")
        print("\n请检查：")
        print("1. QMT是否已连接行情服务器")
        print("2. 网络连接是否正常")
        print("3. 股票代码是否正确")
        print("4. 日期格式是否正确")

def process_day_data(day_data, date_str, target_times, stock_code):
    """处理单日数据，查找目标时间点"""
    day_target_data = []

    for time_str in target_times:
        matching_data = find_time_data(day_data, time_str)

        if matching_data is not None:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]

            # 添加到结果列表
            day_target_data.append({
                '股票代码': stock_code,
                '股票名称': '格力电器',
                '日期': date_str,
                '目标时间': f"{time_str[:2]}:{time_str[2:]}",
                '实际时间': actual_time,
                '开盘价': round(row['open'], 2),
                '最高价': round(row['high'], 2),
                '最低价': round(row['low'], 2),
                '收盘价': round(row['close'], 2),
                '成交量': int(row['volume']),
                '成交额': round(row['amount'], 2)
            })

            print(f"  ✅ {time_str[:2]}:{time_str[2:]} - 股价: {row['close']:.2f} 元")
        else:
            print(f"  ❌ {time_str[:2]}:{time_str[2:]} - 未找到数据")

    return day_target_data

def find_time_data(df, time_str):
    """查找指定时间的数据"""
    # 尝试多种时间格式匹配
    patterns = [
        time_str,           # 0935
        f"{time_str}00",    # 093500
        f"{time_str}30",    # 093530
        f"{time_str}15",    # 093515
        f"{time_str}45"     # 093545
    ]

    for pattern in patterns:
        matches = df[df.index.str.contains(pattern)]
        if not matches.empty:
            return matches

    return None

def save_all_target_data_csv(data_list, start_date, end_date):
    """保存所有目标时间点数据到CSV文件"""
    try:
        # 创建DataFrame
        df_save = pd.DataFrame(data_list)

        # 按日期和时间排序
        df_save = df_save.sort_values(['日期', '目标时间'])

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_{start_date}到{end_date}_目标时间点_{timestamp}.csv"

        # 保存到CSV文件
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')

        return filename

    except Exception as e:
        print(f"❌ 保存CSV文件失败: {str(e)}")
        return None

def show_summary_statistics(data_list):
    """显示统计信息"""
    try:
        df = pd.DataFrame(data_list)

        print(f"\n📊 数据统计信息:")
        print(f"   总记录数: {len(df)}")
        print(f"   交易日数: {df['日期'].nunique()}")
        print(f"   日期范围: {df['日期'].min()} 到 {df['日期'].max()}")

        # 按时间点统计
        print(f"\n⏰ 各时间点数据量:")
        time_counts = df['目标时间'].value_counts().sort_index()
        for time_point, count in time_counts.items():
            print(f"   {time_point}: {count} 条")

        # 股价统计
        print(f"\n💰 股价统计 (收盘价):")
        close_prices = df['收盘价']
        print(f"   最高价: {close_prices.max():.2f} 元")
        print(f"   最低价: {close_prices.min():.2f} 元")
        print(f"   平均价: {close_prices.mean():.2f} 元")
        print(f"   价格波动: {close_prices.max() - close_prices.min():.2f} 元")

        # 成交量统计
        print(f"\n📈 成交量统计:")
        volumes = df['成交量']
        print(f"   总成交量: {volumes.sum():,.0f} 股")
        print(f"   平均成交量: {volumes.mean():,.0f} 股")
        print(f"   最大成交量: {volumes.max():,.0f} 股")

    except Exception as e:
        print(f"❌ 统计信息计算失败: {str(e)}")

"""
程序说明：

这个程序用于获取格力电器在指定时间范围内每个交易日的三个时间点股价：
- 时间范围：2025年4月30日 到 2025年8月4日
- 目标时间点：9:35、9:40、9:45
- 获取期间内所有交易日的目标时间点数据

生成的CSV文件：
- 文件名：格力电器_20250430到20250804_目标时间点_时间戳.csv
- 内容：包含时间范围内所有交易日的目标时间点股价数据

CSV文件格式：
股票代码,股票名称,日期,目标时间,实际时间,开盘价,最高价,最低价,收盘价,成交量,成交额
000651.SZ,格力电器,20250430,9:35,20250430093500,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
000651.SZ,格力电器,20250430,9:40,20250430094000,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
000651.SZ,格力电器,20250430,9:45,20250430094500,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
000651.SZ,格力电器,20250501,9:35,20250501093500,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
... (每个交易日3条记录)

数据特点：
- 按日期和时间排序
- 包含完整的股票信息
- 自动跳过非交易日
- 提供详细的统计信息

使用要求：
- 确保QMT已连接行情服务器
- 下载000651.SZ的1分钟历史数据
- 数据时间范围要包含2025年4月30日到8月4日
- 如果包含未来日期，只会获取到已有的历史数据

注意事项：
- 程序会自动处理所有交易日
- 非交易日（周末、节假日）会被自动跳过
- CSV文件使用UTF-8编码，支持中文显示
- 文件名包含时间戳，避免覆盖
"""
