# coding:gbk
"""
格力电器5分钟版
使用5分钟K线数据，下载快，数据量小，精度足够
获取9:35、9:40、9:45时间点股价
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数 - 使用5分钟数据"""
    
    print("=" * 70)
    print("📊 格力电器5分钟版")
    print("⚡ 使用5分钟K线数据 - 快速、高效、精准")
    print("📅 时间范围: 2025年4月30日 - 7月31日")
    print("🎯 目标时间: 9:35、9:40、9:45")
    print("💡 优势: 数据量小(约1/5)，下载快，精度完全够用")
    print("=" * 70)
    
    # 执行主要任务
    execute_5min_task(ContextInfo)

def execute_5min_task(ContextInfo):
    """执行5分钟数据任务"""
    
    # 参数设置
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📋 任务参数:")
    print(f"   股票: {stock_code} (格力电器)")
    print(f"   周期: 5分钟K线")
    print(f"   时间: {start_date} - {end_date}")
    print(f"   目标: {', '.join(target_times)}")
    
    try:
        print(f"\n🔄 开始获取5分钟数据...")
        
        # 获取5分钟数据
        data = ContextInfo.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='5m',  # 5分钟周期
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        print(f"✅ 数据获取完成")
        
        # 检查数据
        if not data or stock_code not in data or data[stock_code].empty:
            print(f"❌ 5分钟数据为空")
            show_5min_download_guide()
            return
        
        df = data[stock_code]
        print(f"📊 获取到 {len(df):,} 条5分钟K线")
        
        # 显示数据概况
        first_time = df.index[0]
        last_time = df.index[-1]
        print(f"📅 数据范围: {first_time} 到 {last_time}")
        
        # 处理目标时间点
        results = process_5min_data(df, stock_code, target_times)
        
        if results:
            # 保存结果
            filename = save_5min_results(results, start_date, end_date)
            print(f"\n🎉 任务完成!")
            print(f"📁 文件: {filename}")
            print(f"📊 记录: {len(results)} 条")
        else:
            print(f"❌ 未找到任何目标时间点数据")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")
        show_5min_download_guide()

def process_5min_data(df, stock_code, target_times):
    """处理5分钟数据"""
    
    results = []
    
    # 获取所有交易日
    all_dates = set()
    for index in df.index:
        all_dates.add(index[:8])
    
    sorted_dates = sorted(all_dates)
    print(f"\n📅 处理 {len(sorted_dates)} 个交易日:")
    
    for i, date_str in enumerate(sorted_dates, 1):
        # 获取当日5分钟数据
        day_data = df[df.index.str.startswith(date_str)]
        
        if day_data.empty:
            continue
        
        print(f"  {i:2d}. {date_str}: {len(day_data)} 条5分钟K线", end=" -> ")
        
        day_results = []
        for target_time in target_times:
            found_data = find_5min_data(day_data, target_time)
            
            if found_data is not None:
                result = {
                    '股票代码': stock_code,
                    '股票名称': '格力电器',
                    '日期': date_str,
                    '目标时间': target_time,
                    '实际时间': found_data.name,
                    '5分钟开盘': round(found_data['open'], 2),
                    '5分钟最高': round(found_data['high'], 2),
                    '5分钟最低': round(found_data['low'], 2),
                    '5分钟收盘': round(found_data['close'], 2),
                    '成交量': int(found_data['volume']),
                    '成交额': round(found_data['amount'], 2)
                }
                results.append(result)
                day_results.append(f"{target_time}:{result['5分钟收盘']:.2f}")
        
        if day_results:
            print(f"✅ {', '.join(day_results)}")
        else:
            print(f"❌ 无数据")
    
    return results

def find_5min_data(day_data, target_time):
    """查找5分钟K线数据"""
    
    # 5分钟K线时间匹配策略
    if target_time == "0935":
        # 9:35的价格在9:35的5分钟K线中
        # 如果没有9:35，则查找9:30的K线
        patterns = ["0935", "0930"]
    elif target_time == "0940":
        # 9:40的价格在9:40的5分钟K线中
        patterns = ["0940"]
    elif target_time == "0945":
        # 9:45的价格在9:45的5分钟K线中
        patterns = ["0945"]
    else:
        patterns = [target_time]
    
    # 按优先级查找
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def save_5min_results(results, start_date, end_date):
    """保存5分钟数据结果"""
    
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_5分钟K线_{start_date}到{end_date}_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 显示统计
        print(f"\n💾 保存统计:")
        print(f"   文件名: {filename}")
        print(f"   总记录: {len(df)} 条")
        print(f"   交易日: {df['日期'].nunique()} 天")
        print(f"   时间点: {len(df['目标时间'].unique())} 个")
        
        if '5分钟收盘' in df.columns:
            min_price = df['5分钟收盘'].min()
            max_price = df['5分钟收盘'].max()
            avg_price = df['5分钟收盘'].mean()
            print(f"   价格范围: {min_price:.2f} - {max_price:.2f} 元")
            print(f"   平均价格: {avg_price:.2f} 元")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

def show_5min_download_guide():
    """显示5分钟数据下载指南"""
    
    print(f"\n📖 5分钟数据下载指南:")
    print(f"=" * 50)
    print(f"1. 打开QMT -> 操作 -> 数据管理")
    print(f"2. 设置参数:")
    print(f"   📊 市场: 深圳A股")
    print(f"   🏢 股票: 000651 (格力电器)")
    print(f"   ⏰ 周期: 5分钟 ⭐ 推荐")
    print(f"   📅 开始: 2025-04-30")
    print(f"   📅 结束: 2025-07-31")
    print(f"3. 点击下载数据")
    print(f"4. 等待完成后重新运行")
    print(f"")
    print(f"💡 5分钟数据优势:")
    print(f"   ⚡ 下载速度快 (约1分钟数据的1/5)")
    print(f"   💾 存储空间小")
    print(f"   🎯 精度完全够用")
    print(f"   📊 包含完整OHLC信息")

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 使用说明
"""
📊 5分钟版本优势:

⚡ 性能优势:
- 数据量是1分钟的1/5
- 下载速度快5倍
- 存储空间小5倍
- 处理速度快

🎯 精度保证:
- 5分钟K线包含完整OHLC数据
- 目标时间点精度完全够用
- 9:35、9:40、9:45都能准确获取

📈 数据说明:
- 9:35数据: 来自9:35的5分钟K线收盘价
- 9:40数据: 来自9:40的5分钟K线收盘价  
- 9:45数据: 来自9:45的5分钟K线收盘价

💡 推荐理由:
- 对于分析股价趋势，5分钟精度完全足够
- 大幅减少数据下载和处理时间
- 降低存储和网络压力
- 提高策略运行效率

🚀 使用步骤:
1. 下载5分钟历史数据
2. 运行此策略
3. 获得CSV结果文件
"""
