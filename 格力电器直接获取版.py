# coding:gbk
"""
格力电器直接获取版
使用get_history_data直接从服务器获取历史数据
无需预先下载，即时获取，使用5分钟数据
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数 - 直接获取历史数据"""
    
    print("=" * 70)
    print("🌐 格力电器直接获取版")
    print("📡 使用get_history_data直接从服务器获取数据")
    print("⚡ 5分钟K线数据，无需预先下载")
    print("🎯 目标时间: 9:35、9:40、9:45")
    print("=" * 70)
    
    # 执行主要任务
    execute_direct_fetch(ContextInfo)

def execute_direct_fetch(ContextInfo):
    """执行直接获取数据任务"""
    
    # 参数设置
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📋 任务参数:")
    print(f"   股票: {stock_code} (格力电器)")
    print(f"   周期: 5分钟K线")
    print(f"   时间: {start_date} - {end_date}")
    print(f"   目标: {', '.join(target_times)}")
    print(f"   方法: get_history_data (直接获取)")
    
    try:
        print(f"\n🌐 开始从服务器获取历史数据...")
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 使用get_history_data直接获取历史数据
        # 根据API文档，get_history_data的参数格式应该是：
        # get_history_data(stock_code, period, start_date, end_date, fields=None, dividend_type='none')
        data = ContextInfo.get_history_data(
            stock_code,      # 股票代码
            '5m',           # 周期：5分钟
            start_date,     # 开始日期
            end_date        # 结束日期
        )
        
        print(f"✅ 服务器数据获取完成")
        
        # 检查数据
        if data is None or data.empty:
            print(f"❌ 服务器返回数据为空")
            print(f"💡 可能原因:")
            print(f"   1. 网络连接问题")
            print(f"   2. 服务器数据不可用")
            print(f"   3. 股票代码或时间范围错误")
            print(f"   4. QMT服务未正常连接")
            return
        
        print(f"📊 获取到 {len(data):,} 条5分钟K线")
        
        # 显示数据概况
        first_time = data.index[0]
        last_time = data.index[-1]
        print(f"📅 数据范围: {first_time} 到 {last_time}")
        
        # 处理目标时间点
        results = process_direct_data(data, stock_code, target_times)
        
        if results:
            # 保存结果
            filename = save_direct_results(results, start_date, end_date)
            print(f"\n🎉 任务完成!")
            print(f"📁 文件: {filename}")
            print(f"📊 记录: {len(results)} 条")
            
            # 显示部分结果
            show_sample_results(results)
        else:
            print(f"❌ 未找到任何目标时间点数据")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")
        print(f"💡 解决建议:")
        print(f"   1. 检查网络连接")
        print(f"   2. 确认QMT正常运行")
        print(f"   3. 验证股票代码和时间范围")
        print(f"   4. 重新启动QMT客户端")

def process_direct_data(data, stock_code, target_times):
    """处理直接获取的数据"""
    
    results = []
    
    # 获取所有交易日
    all_dates = set()
    for index in data.index:
        all_dates.add(index[:8])
    
    sorted_dates = sorted(all_dates)
    print(f"\n📅 处理 {len(sorted_dates)} 个交易日:")
    
    for i, date_str in enumerate(sorted_dates, 1):
        # 获取当日5分钟数据
        day_data = data[data.index.str.startswith(date_str)]
        
        if day_data.empty:
            continue
        
        print(f"  {i:2d}. {date_str}: {len(day_data)} 条5分钟K线", end=" -> ")
        
        day_results = []
        for target_time in target_times:
            found_data = find_direct_time_data(day_data, target_time)
            
            if found_data is not None:
                result = {
                    '股票代码': stock_code,
                    '股票名称': '格力电器',
                    '日期': date_str,
                    '目标时间': target_time,
                    '实际时间': found_data.name,
                    '开盘价': round(found_data['open'], 2),
                    '最高价': round(found_data['high'], 2),
                    '最低价': round(found_data['low'], 2),
                    '收盘价': round(found_data['close'], 2),
                    '成交量': int(found_data['volume']),
                    '成交额': round(found_data['amount'], 2)
                }
                results.append(result)
                day_results.append(f"{target_time}:{result['收盘价']:.2f}")
        
        if day_results:
            print(f"✅ {', '.join(day_results)}")
        else:
            print(f"❌ 无数据")
    
    return results

def find_direct_time_data(day_data, target_time):
    """查找5分钟K线数据 - 直接获取版本"""
    
    # 5分钟K线时间匹配策略
    if target_time == "0935":
        # 9:35的价格在9:35的5分钟K线中，如果没有则查找9:30
        patterns = ["0935", "0930"]
    elif target_time == "0940":
        # 9:40的价格在9:40的5分钟K线中
        patterns = ["0940"]
    elif target_time == "0945":
        # 9:45的价格在9:45的5分钟K线中
        patterns = ["0945"]
    else:
        patterns = [target_time]
    
    # 按优先级查找
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def save_direct_results(results, start_date, end_date):
    """保存直接获取的数据结果"""
    
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_直接获取_{start_date}到{end_date}_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 显示统计
        print(f"\n💾 保存统计:")
        print(f"   文件名: {filename}")
        print(f"   总记录: {len(df)} 条")
        print(f"   交易日: {df['日期'].nunique()} 天")
        print(f"   时间点: {len(df['目标时间'].unique())} 个")
        
        if '收盘价' in df.columns:
            min_price = df['收盘价'].min()
            max_price = df['收盘价'].max()
            avg_price = df['收盘价'].mean()
            print(f"   价格范围: {min_price:.2f} - {max_price:.2f} 元")
            print(f"   平均价格: {avg_price:.2f} 元")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

def show_sample_results(results):
    """显示部分结果样例"""
    
    print(f"\n📋 结果样例 (前5条):")
    print("-" * 60)
    
    for i, result in enumerate(results[:5], 1):
        print(f"{i}. {result['日期']} {result['目标时间']}: {result['收盘价']:.2f}元")
    
    if len(results) > 5:
        print(f"   ... (共{len(results)}条记录)")

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 使用说明
"""
🌐 直接获取版本优势:

✅ 无需预下载:
- 不需要提前在数据管理中下载数据
- 直接从QMT服务器实时获取
- 节省本地存储空间

⚡ 即时获取:
- 运行策略即可获取数据
- 无需等待数据下载过程
- 适合临时分析需求

🎯 API说明:
get_history_data(stock_code, period, start_date, end_date, fields, dividend_type, fill_data)

参数说明:
- stock_code: 股票代码，如"000651.SZ"
- period: 周期，如"5m"表示5分钟
- start_date: 开始日期，格式"20250430"
- end_date: 结束日期，格式"20250731"
- fields: 字段列表，如['open','high','low','close','volume','amount']
- dividend_type: 复权类型，'none'表示不复权
- fill_data: 是否填充数据，True表示填充

🚀 使用场景:
- 临时数据分析
- 快速验证策略
- 无需长期存储数据
- 网络环境良好的情况

💡 注意事项:
- 需要网络连接
- 依赖QMT服务器状态
- 大量数据获取可能较慢
- 建议在交易时间外使用
"""
