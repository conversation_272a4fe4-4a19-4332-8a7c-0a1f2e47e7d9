# coding:gbk
"""
QMT策略：获取格力电器20250730指定时间点股价
获取时间：9点35分，9点40分，9点45分
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """
    初始化函数
    """
    # 设置格力电器股票代码
    ContextInfo.stock_code = "000651.SZ"  # 格力电器
    
    # 设置查询日期
    ContextInfo.target_date = "20250730"
    
    # 设置目标时间点（分钟级别）
    ContextInfo.target_times = [
        "20250730093500",  # 9点35分
        "20250730094000",  # 9点40分  
        "20250730094500"   # 9点45分
    ]
    
    print("=" * 50)
    print("格力电器股价查询策略初始化完成")
    print(f"股票代码: {ContextInfo.stock_code}")
    print(f"查询日期: {ContextInfo.target_date}")
    print(f"查询时间点: {ContextInfo.target_times}")
    print("=" * 50)

def handlebar(ContextInfo):
    """
    主要执行函数
    """
    try:
        # 方法1：获取指定日期的分钟级数据
        print("\n方法1：获取当日分钟级数据")
        minute_data = get_minute_data_for_date(ContextInfo)
        
        # 方法2：获取指定时间点的数据
        print("\n方法2：获取指定时间点数据")
        specific_time_data = get_specific_time_data(ContextInfo)
        
        # 方法3：获取最新行情数据（如果是实时运行）
        print("\n方法3：获取最新行情数据")
        latest_data = get_latest_market_data(ContextInfo)
        
    except Exception as e:
        print(f"执行出错: {str(e)}")

def get_minute_data_for_date(ContextInfo):
    """
    获取指定日期的分钟级数据
    """
    try:
        # 获取指定日期的1分钟数据
        data = ContextInfo.get_market_data_ex(
            fields=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[ContextInfo.stock_code],
            period='1m',
            start_time=ContextInfo.target_date,
            end_time=ContextInfo.target_date,
            count=-1,
            dividend_type='none',
            fill_data=True,
            subscribe=False
        )
        
        if ContextInfo.stock_code in data and not data[ContextInfo.stock_code].empty:
            df = data[ContextInfo.stock_code]
            print(f"获取到 {len(df)} 条分钟数据")
            
            # 筛选目标时间点的数据
            target_data = []
            for target_time in ContextInfo.target_times:
                # 转换时间格式进行匹配
                time_str = target_time[8:]  # 提取时间部分 HHMMSS
                formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
                
                # 查找最接近的时间点
                matching_rows = df[df.index.str.contains(target_time[8:12])]  # 匹配HHMM
                
                if not matching_rows.empty:
                    row = matching_rows.iloc[0]
                    target_data.append({
                        '时间': target_time,
                        '开盘价': row['open'],
                        '最高价': row['high'],
                        '最低价': row['low'],
                        '收盘价': row['close'],
                        '成交量': row['volume'],
                        '成交额': row['amount']
                    })
            
            # 打印结果
            if target_data:
                print("\n格力电器指定时间点股价信息：")
                print("-" * 80)
                for data_point in target_data:
                    print(f"时间: {data_point['时间']}")
                    print(f"  开盘价: {data_point['开盘价']:.2f}")
                    print(f"  最高价: {data_point['最高价']:.2f}")
                    print(f"  最低价: {data_point['最低价']:.2f}")
                    print(f"  收盘价: {data_point['收盘价']:.2f}")
                    print(f"  成交量: {data_point['成交量']:,.0f}")
                    print(f"  成交额: {data_point['成交额']:,.2f}")
                    print("-" * 40)
            else:
                print("未找到指定时间点的数据")
                
            return target_data
        else:
            print("未获取到数据，可能需要先下载历史数据")
            return None
            
    except Exception as e:
        print(f"获取分钟数据出错: {str(e)}")
        return None

def get_specific_time_data(ContextInfo):
    """
    获取指定时间点的tick数据
    """
    try:
        # 获取tick数据
        tick_data = ContextInfo.get_market_data_ex(
            fields=['time', 'lastPrice', 'open', 'high', 'low', 'volume', 'amount'],
            stock_code=[ContextInfo.stock_code],
            period='tick',
            start_time=ContextInfo.target_date,
            end_time=ContextInfo.target_date,
            count=-1,
            dividend_type='none',
            fill_data=True,
            subscribe=False
        )
        
        if ContextInfo.stock_code in tick_data and not tick_data[ContextInfo.stock_code].empty:
            df = tick_data[ContextInfo.stock_code]
            print(f"获取到 {len(df)} 条tick数据")
            
            # 这里可以进一步处理tick数据
            # 由于tick数据量大，这里只显示数据概况
            print("Tick数据时间范围:")
            print(f"开始时间: {df.index[0]}")
            print(f"结束时间: {df.index[-1]}")
            
            return df
        else:
            print("未获取到tick数据")
            return None
            
    except Exception as e:
        print(f"获取tick数据出错: {str(e)}")
        return None

def get_latest_market_data(ContextInfo):
    """
    获取最新行情数据
    """
    try:
        # 获取最新行情
        latest_data = ContextInfo.get_market_data_ex(
            fields=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[ContextInfo.stock_code],
            period='1d',
            count=1,
            dividend_type='none',
            fill_data=True,
            subscribe=True
        )
        
        if ContextInfo.stock_code in latest_data and not latest_data[ContextInfo.stock_code].empty:
            df = latest_data[ContextInfo.stock_code]
            latest_row = df.iloc[-1]
            
            print("格力电器最新行情:")
            print(f"日期: {df.index[-1]}")
            print(f"开盘价: {latest_row['open']:.2f}")
            print(f"最高价: {latest_row['high']:.2f}")
            print(f"最低价: {latest_row['low']:.2f}")
            print(f"收盘价: {latest_row['close']:.2f}")
            print(f"成交量: {latest_row['volume']:,.0f}")
            print(f"成交额: {latest_row['amount']:,.2f}")
            
            return latest_row
        else:
            print("未获取到最新行情数据")
            return None
            
    except Exception as e:
        print(f"获取最新行情出错: {str(e)}")
        return None

def download_history_data_if_needed(ContextInfo):
    """
    如果需要，下载历史数据
    """
    try:
        # 下载历史分钟数据
        download_history_data(
            ContextInfo.stock_code,
            "1m",
            ContextInfo.target_date,
            ContextInfo.target_date
        )
        print("历史数据下载完成")
    except Exception as e:
        print(f"下载历史数据出错: {str(e)}")

# 使用说明
"""
使用步骤：
1. 确保QMT客户端已连接并登录
2. 在QMT中新建Python策略，复制此代码
3. 运行策略前，建议先下载格力电器的历史数据：
   - 在QMT中选择"数据管理"
   - 下载000651.SZ的1分钟数据，时间范围包含20250730
4. 运行策略查看结果

注意事项：
- 如果查询的是未来日期（如20250730），需要等到该日期才能获取到实际数据
- 如果是历史日期，需要确保已下载相应的历史数据
- 分钟级数据需要在交易时间内才有数据
- 股票代码000651.SZ是格力电器的深交所代码
"""
