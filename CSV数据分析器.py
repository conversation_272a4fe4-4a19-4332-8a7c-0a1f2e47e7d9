# coding:gbk
"""
CSV数据分析器 - QMT策略版
专门用于分析格力电器股价CSV数据的QMT策略
支持自动查找、深度分析和多文件对比
"""

import pandas as pd
import os
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("📊 CSV数据分析器启动")
    print("🔍 专业分析格力电器股价CSV数据")
    # 设置执行标志
    ContextInfo.analysis_done = False

def handlebar(ContextInfo):
    """主执行函数"""

    # 防止重复执行
    if hasattr(ContextInfo, 'analysis_done') and ContextInfo.analysis_done:
        return

    # 只在最后一根K线执行
    if not ContextInfo.is_last_bar():
        return

    print("\n📁 搜索CSV文件...")

    # 设置标志，防止重复执行
    ContextInfo.analysis_done = True

    # 查找CSV文件
    csv_files = find_csv_files()
    
    if not csv_files:
        print("❌ 未找到相关CSV文件")
        print("💡 请确保CSV文件在当前目录下")
        print("💡 支持的文件名关键词：格力电器、000651、本地数据、目标时间点")
        return
    
    print(f"✅ 发现 {len(csv_files)} 个CSV文件:")
    for i, file in enumerate(csv_files, 1):
        try:
            file_size = os.path.getsize(file) / 1024  # KB
            file_time = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   {i}. {file} ({file_size:.1f} KB, {file_time.strftime('%m-%d %H:%M')})")
        except:
            print(f"   {i}. {file}")
    
    # 分析每个文件
    for csv_file in csv_files:
        analyze_csv_file(csv_file)
    
    # 如果有多个文件，进行对比
    if len(csv_files) > 1:
        compare_files(csv_files)

def find_csv_files():
    """智能查找CSV文件"""
    csv_files = []
    current_dir = os.getcwd()
    
    try:
        for file in os.listdir(current_dir):
            if file.endswith('.csv'):
                # 检查关键词
                keywords = ['格力电器', '000651', '本地数据', '目标时间点', '分钟数据', '日线数据']
                if any(keyword in file for keyword in keywords):
                    csv_files.append(file)
    except Exception as e:
        print(f"❌ 搜索文件出错: {str(e)}")
    
    # 按修改时间排序（最新的在前）
    csv_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return csv_files

def analyze_csv_file(filename):
    """深度分析单个CSV文件"""
    try:
        print(f"\n" + "="*80)
        print(f"📋 分析文件: {filename}")
        print("="*80)
        
        # 读取文件
        df = pd.read_csv(filename, encoding='utf-8-sig')
        
        # 基本信息
        file_size = os.path.getsize(filename) / 1024
        file_time = datetime.fromtimestamp(os.path.getmtime(filename))
        
        print(f"📁 文件信息:")
        print(f"   文件名: {filename}")
        print(f"   大小: {file_size:.1f} KB")
        print(f"   修改时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   记录数: {len(df):,}")
        print(f"   字段数: {len(df.columns)}")
        
        # 字段分析
        print(f"\n📊 字段分析:")
        for i, col in enumerate(df.columns, 1):
            non_null = df[col].count()
            null_count = len(df) - non_null
            data_type = str(df[col].dtype)
            print(f"   {i:2d}. {col:<12} - 类型: {data_type:<8} 有效: {non_null:,} 空值: {null_count}")
        
        # 时间范围分析
        if '日期' in df.columns:
            analyze_date_range(df)
        
        # 时间点分析
        if '目标时间' in df.columns:
            analyze_time_points(df)
        
        # 价格分析
        analyze_prices(df)
        
        # 成交量分析
        if '成交量' in df.columns:
            analyze_volume(df)
        
        # 数据预览
        show_data_preview(df)
        
        # 趋势分析
        if '收盘价' in df.columns and '日期' in df.columns:
            analyze_price_trend(df)
        
    except Exception as e:
        print(f"❌ 分析文件 {filename} 出错: {str(e)}")

def analyze_date_range(df):
    """分析日期范围"""
    try:
        dates = df['日期'].unique()
        print(f"\n📅 时间范围:")
        print(f"   起始日期: {min(dates)}")
        print(f"   结束日期: {max(dates)}")
        print(f"   交易日数: {len(dates)}")
        
        # 显示日期列表（最多显示10个）
        sorted_dates = sorted(dates)
        if len(sorted_dates) <= 10:
            print(f"   交易日: {', '.join(sorted_dates)}")
        else:
            print(f"   前5个交易日: {', '.join(sorted_dates[:5])}")
            print(f"   后5个交易日: {', '.join(sorted_dates[-5:])}")
    except Exception as e:
        print(f"❌ 日期分析出错: {str(e)}")

def analyze_time_points(df):
    """分析时间点"""
    try:
        times = df['目标时间'].unique()
        print(f"\n⏰ 时间点分析:")
        print(f"   时间点: {', '.join(sorted(times))}")
        
        time_counts = df['目标时间'].value_counts().sort_index()
        for time_point, count in time_counts.items():
            percentage = (count / len(df)) * 100
            print(f"   {time_point}: {count:,} 条记录 ({percentage:.1f}%)")
    except Exception as e:
        print(f"❌ 时间点分析出错: {str(e)}")

def analyze_prices(df):
    """分析价格数据"""
    try:
        price_columns = ['开盘价', '最高价', '最低价', '收盘价']
        available_cols = [col for col in price_columns if col in df.columns]
        
        if available_cols:
            print(f"\n💰 价格分析:")
            for col in available_cols:
                stats = df[col].describe()
                print(f"   {col}:")
                print(f"     最小: {stats['min']:.2f} 元  最大: {stats['max']:.2f} 元")
                print(f"     平均: {stats['mean']:.2f} 元  标准差: {stats['std']:.2f} 元")
                print(f"     中位数: {stats['50%']:.2f} 元  波动范围: {stats['max']-stats['min']:.2f} 元")
    except Exception as e:
        print(f"❌ 价格分析出错: {str(e)}")

def analyze_volume(df):
    """分析成交量"""
    try:
        volume_stats = df['成交量'].describe()
        print(f"\n📈 成交量分析:")
        print(f"   最小成交量: {volume_stats['min']:,.0f}")
        print(f"   最大成交量: {volume_stats['max']:,.0f}")
        print(f"   平均成交量: {volume_stats['mean']:,.0f}")
        print(f"   总成交量: {df['成交量'].sum():,.0f}")
        
        if '成交额' in df.columns:
            amount_stats = df['成交额'].describe()
            print(f"   总成交额: {df['成交额'].sum():,.0f} 元")
            print(f"   平均成交额: {amount_stats['mean']:,.0f} 元")
    except Exception as e:
        print(f"❌ 成交量分析出错: {str(e)}")

def show_data_preview(df):
    """显示数据预览"""
    try:
        print(f"\n📋 数据预览 (最新5条):")
        preview_df = df.tail(5).copy()
        
        # 格式化数值列
        for col in ['开盘价', '最高价', '最低价', '收盘价']:
            if col in preview_df.columns:
                preview_df[col] = preview_df[col].apply(lambda x: f"{x:.2f}")
        
        if '成交量' in preview_df.columns:
            preview_df['成交量'] = preview_df['成交量'].apply(lambda x: f"{x:,}")
        
        if '成交额' in preview_df.columns:
            preview_df['成交额'] = preview_df['成交额'].apply(lambda x: f"{x:,.0f}")
        
        print(preview_df.to_string(index=False))
    except Exception as e:
        print(f"❌ 数据预览出错: {str(e)}")

def analyze_price_trend(df):
    """分析价格趋势"""
    try:
        print(f"\n📊 价格趋势分析:")
        
        # 按日期计算平均价格
        daily_avg = df.groupby('日期')['收盘价'].mean().sort_index()
        
        if len(daily_avg) >= 2:
            first_price = daily_avg.iloc[0]
            last_price = daily_avg.iloc[-1]
            change = last_price - first_price
            change_pct = (change / first_price) * 100
            
            print(f"   期间首价: {first_price:.2f} 元")
            print(f"   期间末价: {last_price:.2f} 元")
            print(f"   价格变化: {change:+.2f} 元 ({change_pct:+.2f}%)")
            
            # 计算最大涨跌幅
            max_price = daily_avg.max()
            min_price = daily_avg.min()
            max_change = ((max_price - first_price) / first_price) * 100
            min_change = ((min_price - first_price) / first_price) * 100
            
            print(f"   期间最高: {max_price:.2f} 元 ({max_change:+.2f}%)")
            print(f"   期间最低: {min_price:.2f} 元 ({min_change:+.2f}%)")
            print(f"   最大波动: {max_price - min_price:.2f} 元")
    except Exception as e:
        print(f"❌ 趋势分析出错: {str(e)}")

def compare_files(csv_files):
    """对比多个CSV文件"""
    try:
        print(f"\n" + "="*80)
        print(f"🔄 多文件对比分析 (共{len(csv_files)}个文件)")
        print("="*80)
        
        comparison_data = []
        
        for filename in csv_files:
            try:
                df = pd.read_csv(filename, encoding='utf-8-sig')
                file_size = os.path.getsize(filename) / 1024
                
                # 截取文件名
                short_name = filename[:25] + '...' if len(filename) > 25 else filename
                
                data = {
                    '文件名': short_name,
                    '记录数': f"{len(df):,}",
                    '大小(KB)': f"{file_size:.1f}",
                }
                
                if '日期' in df.columns:
                    data['交易日'] = df['日期'].nunique()
                    data['日期范围'] = f"{df['日期'].min()}-{df['日期'].max()}"
                
                if '目标时间' in df.columns:
                    data['时间点'] = len(df['目标时间'].unique())
                
                if '收盘价' in df.columns:
                    data['平均价'] = f"{df['收盘价'].mean():.2f}"
                    data['价格范围'] = f"{df['收盘价'].min():.2f}-{df['收盘价'].max():.2f}"
                
                comparison_data.append(data)
                
            except Exception as e:
                print(f"❌ 处理文件 {filename} 失败: {str(e)}")
        
        if comparison_data:
            comp_df = pd.DataFrame(comparison_data)
            print(comp_df.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 对比分析出错: {str(e)}")

# 使用说明
"""
📖 CSV数据分析器使用说明：

🎯 主要功能：
1. 智能搜索CSV文件
2. 深度数据分析
3. 多文件对比
4. 趋势分析

🚀 使用方法：
1. 将CSV文件放在QMT运行目录
2. 运行此策略
3. 查看详细分析报告

📊 分析内容：
- 文件基本信息
- 字段完整性
- 时间范围统计
- 价格趋势分析
- 成交量分析
- 多文件对比

💡 支持文件：
- 格力电器相关CSV文件
- 包含股价、时间等字段
- UTF-8编码格式
"""
