# coding:gbk
"""
格力电器一次性执行版
解决重复执行问题，只在策略启动时执行一次
使用get_local_data获取本地数据并保存CSV
"""

import pandas as pd
from datetime import datetime

# 全局标志，防止重复执行
EXECUTED = False

def init(ContextInfo):
    """初始化 - 在这里执行主要逻辑"""
    global EXECUTED
    
    if EXECUTED:
        print("⚠️ 已经执行过，跳过重复执行")
        return
    
    print("🏠 格力电器一次性执行版")
    print("📅 从本地获取2025年4月30日-7月31日数据")
    print("🎯 提取9:35、9:40、9:45时间点股价")
    
    # 执行主要逻辑
    execute_main_logic(ContextInfo)
    
    # 设置执行标志
    EXECUTED = True
    print("✅ 执行完成，策略将不再重复运行")

def handlebar(ContextInfo):
    """K线处理函数 - 空函数，防止重复执行"""
    pass

def execute_main_logic(ContextInfo):
    """主要执行逻辑"""
    
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📊 开始处理 {stock_code} 本地数据...")
    
    try:
        # 从本地获取数据
        print("📡 正在从本地读取分钟数据...")
        
        data = ContextInfo.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if stock_code in data and not data[stock_code].empty:
            df = data[stock_code]
            print(f"✅ 获取到 {len(df):,} 条本地数据")
            
            # 显示数据范围
            first_date = df.index[0][:8]
            last_date = df.index[-1][:8]
            print(f"📅 数据时间范围: {first_date} 到 {last_date}")
            
            # 提取目标时间点数据
            results = extract_target_data(df, stock_code, target_times)
            
            # 保存结果
            if results:
                filename = save_to_csv(results, start_date, end_date)
                print(f"🎯 成功提取 {len(results)} 个目标时间点")
                print(f"📁 数据已保存到: {filename}")
            else:
                print("❌ 未找到任何目标时间点数据")
                
        else:
            print("❌ 本地无数据")
            print("💡 请先在QMT数据管理中下载历史数据")
            print("💡 路径: 操作 -> 数据管理 -> 选择000651.SZ -> 1分钟 -> 下载")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")
        print("💡 可能原因:")
        print("   1. 本地数据未下载")
        print("   2. 数据路径不正确")
        print("   3. 数据格式问题")

def extract_target_data(df, stock_code, target_times):
    """提取目标时间点数据"""
    
    results = []
    dates_processed = set()
    
    print(f"\n📊 开始提取目标时间点...")
    
    for index, row in df.iterrows():
        date_str = index[:8]  # 提取日期YYYYMMDD
        
        if date_str in dates_processed:
            continue
            
        # 获取当天数据
        day_data = df[df.index.str.startswith(date_str)]
        
        if not day_data.empty:
            print(f"📅 处理 {date_str}: {len(day_data)} 条数据", end=" -> ")
            
            day_results = []
            # 查找目标时间点
            for target_time in target_times:
                found_data = find_time_data(day_data, target_time)
                if found_data is not None:
                    result = {
                        '股票代码': stock_code,
                        '股票名称': '格力电器',
                        '日期': date_str,
                        '目标时间': target_time,
                        '实际时间': found_data.name,
                        '开盘价': round(found_data['open'], 2),
                        '最高价': round(found_data['high'], 2),
                        '最低价': round(found_data['low'], 2),
                        '收盘价': round(found_data['close'], 2),
                        '成交量': int(found_data['volume']),
                        '成交额': round(found_data['amount'], 2)
                    }
                    results.append(result)
                    day_results.append(f"{target_time}:{result['收盘价']:.2f}")
            
            if day_results:
                print(f"✅ {', '.join(day_results)}")
            else:
                print("❌ 无目标时间点")
            
            dates_processed.add(date_str)
    
    return results

def find_time_data(day_data, target_time):
    """查找指定时间的数据"""
    
    # 尝试不同的时间格式匹配
    patterns = [
        target_time + "00",  # 0935 -> 093500
        target_time + "30",  # 0935 -> 093530
        target_time,         # 0935
        target_time[:3]      # 0935 -> 093
    ]
    
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def save_to_csv(results, start_date, end_date):
    """保存结果到CSV"""
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_本地数据_{start_date}到{end_date}_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 显示统计信息
        print(f"\n📊 保存统计:")
        print(f"   文件名: {filename}")
        print(f"   记录数: {len(df)}")
        print(f"   交易日: {df['日期'].nunique()}")
        print(f"   日期范围: {df['日期'].min()} - {df['日期'].max()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

# 使用说明
"""
🚀 一次性执行版特点：

✅ 解决问题：
- 防止重复执行和无限循环
- 避免生成大量重复CSV文件
- 终端不再无限滚动

🎯 执行机制：
- 只在init函数中执行一次
- 使用全局标志防止重复
- handlebar函数为空，不执行任何操作

📝 使用步骤：
1. 确保已下载本地数据（QMT数据管理）
2. 复制此代码到QMT策略编辑器
3. 点击运行，等待执行完成
4. 查看生成的CSV文件

💡 优势：
- 执行一次即停止
- 快速高效
- 结果清晰
- 无重复文件
"""
