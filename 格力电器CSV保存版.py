# coding:gbk
"""
格力电器股价查询 - CSV保存版
功能：获取格力电器指定时间点股价并保存为CSV文件
时间点：9:35, 9:40, 9:45
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("=" * 60)
    print("格力电器股价查询 - CSV保存版")
    print("功能：获取指定时间点股价并保存为CSV文件")
    print("目标时间：9:35, 9:40, 9:45")
    print("=" * 60)

def handlebar(ContextInfo):
    """主执行函数"""

    stock_code = "000651.SZ"  # 格力电器
    target_date = "20250730"  # 目标日期

    print(f"\n开始查询 {stock_code} (格力电器) 在 {target_date} 的股价数据...")

    try:
        # 直接获取目标日期的分钟数据
        print(f"\n=== 获取 {target_date} 的分钟数据 ===")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=target_date,
            end_time=target_date,
            count=-1,
            subscribe=False
        )

        target_data_found = False

        if stock_code in minute_data and not minute_data[stock_code].empty:
            df_minute = minute_data[stock_code]
            print(f"成功获取目标日期 {len(df_minute)} 条分钟数据")

            # 处理并保存指定时间点数据
            target_data = process_and_save_target_times(df_minute, target_date)

            if target_data:
                target_data_found = True
                print(f"\n✅ 成功获取并保存 {target_date} 的目标时间点数据！")
            else:
                print(f"\n❌ 未找到 {target_date} 的目标时间点数据")

        if not target_data_found:
            print(f"\n⚠️  未获取到 {target_date} 的数据")
            print("可能的原因：")
            print("1. 该日期是未来日期，数据尚未产生")
            print("2. 该日期是非交易日")
            print("3. 需要先下载历史数据")
            print("4. 网络连接问题")

            # 提示用户如何获取数据
            print(f"\n💡 如果今天是 {target_date} 或之后的日期：")
            print("1. 确保QMT已连接行情服务器")
            print("2. 在QMT中下载000651.SZ的1分钟历史数据")
            print("3. 确保数据时间范围包含20250730")

    except Exception as e:
        print(f"❌ 查询出错: {str(e)}")
        print("\n可能的解决方案：")
        print("1. 检查网络连接")
        print("2. 确保QMT已登录")
        print("3. 下载相应的历史数据")

def process_and_save_target_times(df, date_str):
    """处理并保存指定时间点的数据"""
    
    target_times = ["0935", "0940", "0945"]
    target_data_list = []
    
    print(f"\n格力电器 {date_str} 指定时间点股价：")
    print("=" * 70)
    
    for time_str in target_times:
        matching_data = find_matching_time(df, time_str)
        
        if matching_data is not None:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]
            
            # 添加到数据列表
            target_data_list.append({
                '股票代码': '000651.SZ',
                '股票名称': '格力电器',
                '日期': date_str,
                '目标时间': f"{time_str[:2]}:{time_str[2:]}",
                '实际时间': actual_time,
                '开盘价': round(row['open'], 2),
                '最高价': round(row['high'], 2),
                '最低价': round(row['low'], 2),
                '收盘价': round(row['close'], 2),
                '成交量': int(row['volume']),
                '成交额': round(row['amount'], 2)
            })
            
            print(f"✅ {time_str[:2]}:{time_str[2:]} - 股价: {row['close']:.2f} 元")
        else:
            print(f"❌ {time_str[:2]}:{time_str[2:]} - 无数据")
    
    # 保存目标时间点数据
    if target_data_list:
        filename = save_target_times_csv(target_data_list, date_str)
        print(f"\n📁 目标时间点数据已保存: {filename}")
    
    return target_data_list

def find_matching_time(df, time_str):
    """查找匹配的时间数据"""
    patterns = [time_str, f"{time_str}00", f"{time_str}30"]
    
    for pattern in patterns:
        matches = df[df.index.str.contains(pattern)]
        if not matches.empty:
            return matches
    return None

def save_target_times_csv(data_list, date_str):
    """保存目标时间点数据为CSV"""
    try:
        df_save = pd.DataFrame(data_list)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_目标时间点_{date_str}_{timestamp}.csv"
        
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        return filename
    except Exception as e:
        print(f"❌ 保存目标时间点数据失败: {str(e)}")
        return None



"""
使用说明：

专门用于获取格力电器2025年7月30日三个时间点的股价：
- 9:35
- 9:40
- 9:45

使用步骤：
1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 点击"运行"执行查询
4. 查看生成的CSV文件

生成的CSV文件：
- 格力电器_目标时间点_20250730_时间戳.csv

CSV文件内容：
- 股票代码：000651.SZ
- 股票名称：格力电器
- 日期：20250730
- 目标时间：9:35, 9:40, 9:45
- 实际时间：具体的时间戳
- 股价数据：开盘价、最高价、最低价、收盘价、成交量、成交额

重要提醒：
- 只有在2025年7月30日当天或之后才能获取到实际数据
- 如果是未来日期，程序会提示无法获取数据
- 确保在QMT中下载了000651.SZ的1分钟历史数据
- CSV文件使用UTF-8编码，支持中文显示
"""
