# coding:gbk
"""
CSV文件查看器
用于查看格力电器股价查询生成的CSV文件
"""

import pandas as pd
import os
import glob

def main():
    """主函数"""
    print("=" * 60)
    print("格力电器股价数据 - CSV文件查看器")
    print("=" * 60)
    
    # 查找当前目录下的CSV文件
    csv_files = find_csv_files()
    
    if not csv_files:
        print("❌ 当前目录下没有找到相关的CSV文件")
        print("请先运行股价查询策略生成CSV文件")
        return
    
    # 显示文件列表
    print(f"\n📁 找到 {len(csv_files)} 个CSV文件：")
    for i, file in enumerate(csv_files, 1):
        file_size = os.path.getsize(file) / 1024  # KB
        print(f"{i:2d}. {file} ({file_size:.1f} KB)")
    
    # 让用户选择文件
    while True:
        try:
            choice = input(f"\n请选择要查看的文件 (1-{len(csv_files)}, 0=退出): ")
            
            if choice == '0':
                print("退出程序")
                break
            
            file_index = int(choice) - 1
            if 0 <= file_index < len(csv_files):
                view_csv_file(csv_files[file_index])
            else:
                print("❌ 无效的选择，请重新输入")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n程序已退出")
            break

def find_csv_files():
    """查找相关的CSV文件"""
    patterns = [
        "格力电器_*.csv",
        "*格力电器*.csv",
        "*000651*.csv"
    ]
    
    csv_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        csv_files.extend(files)
    
    # 去重并按修改时间排序
    csv_files = list(set(csv_files))
    csv_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    return csv_files

def view_csv_file(filename):
    """查看CSV文件内容"""
    try:
        print(f"\n" + "=" * 80)
        print(f"📄 查看文件: {filename}")
        print("=" * 80)
        
        # 读取CSV文件
        df = pd.read_csv(filename, encoding='utf-8-sig')
        
        print(f"📊 数据概况:")
        print(f"   行数: {len(df)}")
        print(f"   列数: {len(df.columns)}")
        print(f"   列名: {', '.join(df.columns.tolist())}")
        
        # 显示数据内容
        print(f"\n📋 数据内容:")
        
        if len(df) <= 20:
            # 如果数据不多，显示全部
            print(df.to_string(index=False))
        else:
            # 如果数据很多，显示前10行和后5行
            print("前10行:")
            print(df.head(10).to_string(index=False))
            print("\n...")
            print(f"\n后5行:")
            print(df.tail(5).to_string(index=False))
        
        # 如果是股价数据，显示统计信息
        if '收盘价' in df.columns:
            show_price_statistics(df)
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")

def show_price_statistics(df):
    """显示股价统计信息"""
    try:
        print(f"\n📈 股价统计信息:")
        
        if '收盘价' in df.columns:
            close_prices = df['收盘价']
            print(f"   最高价: {close_prices.max():.2f} 元")
            print(f"   最低价: {close_prices.min():.2f} 元")
            print(f"   平均价: {close_prices.mean():.2f} 元")
            print(f"   价格范围: {close_prices.max() - close_prices.min():.2f} 元")
        
        if '成交量' in df.columns:
            volumes = df['成交量']
            print(f"   总成交量: {volumes.sum():,.0f} 股")
            print(f"   平均成交量: {volumes.mean():,.0f} 股")
        
        if '成交额' in df.columns:
            amounts = df['成交额']
            print(f"   总成交额: {amounts.sum():,.0f} 元")
            
    except Exception as e:
        print(f"❌ 统计信息计算失败: {str(e)}")

def export_to_excel():
    """将CSV文件转换为Excel文件"""
    csv_files = find_csv_files()
    
    if not csv_files:
        print("❌ 没有找到CSV文件")
        return
    
    try:
        # 创建Excel文件
        excel_filename = f"格力电器股价数据汇总_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file, encoding='utf-8-sig')
                    
                    # 生成工作表名称
                    sheet_name = os.path.splitext(csv_file)[0]
                    if len(sheet_name) > 31:  # Excel工作表名称限制
                        sheet_name = sheet_name[:31]
                    
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"✅ {csv_file} -> {sheet_name}")
                    
                except Exception as e:
                    print(f"❌ 处理 {csv_file} 失败: {str(e)}")
        
        print(f"\n📊 Excel文件已生成: {excel_filename}")
        
    except Exception as e:
        print(f"❌ 生成Excel文件失败: {str(e)}")

if __name__ == "__main__":
    main()

"""
使用说明：

1. 将此文件保存在与CSV文件相同的目录下
2. 运行此程序
3. 选择要查看的CSV文件
4. 查看数据内容和统计信息

功能特点：
- 自动查找格力电器相关的CSV文件
- 显示文件列表和大小
- 查看CSV文件内容
- 显示股价统计信息
- 支持大文件的分页显示

注意事项：
- 确保CSV文件使用UTF-8编码
- 程序会自动处理中文字符
- 支持查看目标时间点、分钟数据、日线数据等各种CSV文件
"""
