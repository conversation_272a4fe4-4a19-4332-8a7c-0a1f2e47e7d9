# coding:gbk
"""
格力电器本地数据简化版
最简单的方式使用get_local_data获取本地数据并保存CSV
专为已下载20250430-20250731数据设计
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("🏠 格力电器本地数据简化版")
    print("📅 读取本地2025年4月30日-7月31日数据")

def handlebar(ContextInfo):
    """主函数"""
    
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📊 开始处理 {stock_code} 本地数据...")
    
    try:
        # 从本地获取数据
        data = ContextInfo.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if stock_code in data and not data[stock_code].empty:
            df = data[stock_code]
            print(f"✅ 获取到 {len(df):,} 条本地数据")
            
            # 提取目标时间点数据
            results = []
            dates_processed = set()
            
            for index, row in df.iterrows():
                date_str = index[:8]  # 提取日期YYYYMMDD
                
                if date_str in dates_processed:
                    continue
                    
                # 获取当天数据
                day_data = df[df.index.str.startswith(date_str)]
                print(f"📅 {date_str}: {len(day_data)} 条数据")
                
                # 查找目标时间点
                for target_time in target_times:
                    found_data = find_time_data(day_data, target_time)
                    if found_data is not None:
                        result = {
                            '股票代码': stock_code,
                            '股票名称': '格力电器',
                            '日期': date_str,
                            '目标时间': target_time,
                            '实际时间': found_data.name,
                            '开盘价': round(found_data['open'], 2),
                            '最高价': round(found_data['high'], 2),
                            '最低价': round(found_data['low'], 2),
                            '收盘价': round(found_data['close'], 2),
                            '成交量': int(found_data['volume']),
                            '成交额': round(found_data['amount'], 2)
                        }
                        results.append(result)
                        print(f"  ✅ {target_time}: {result['收盘价']:.2f}元")
                
                dates_processed.add(date_str)
            
            # 保存结果
            if results:
                save_to_csv(results, start_date, end_date)
            else:
                print("❌ 未找到任何目标时间点数据")
                
        else:
            print("❌ 本地无数据，请先下载历史数据")
            print("💡 下载方法：QMT -> 操作 -> 数据管理")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def find_time_data(day_data, target_time):
    """查找指定时间的数据"""
    # 尝试不同的时间格式匹配
    for suffix in ["00", "30", ""]:
        pattern = target_time + suffix
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    # 如果精确匹配失败，尝试模糊匹配
    pattern = target_time[:3]  # 例如 "093"
    matches = day_data[day_data.index.str.contains(pattern)]
    if not matches.empty:
        return matches.iloc[0]
    
    return None

def save_to_csv(results, start_date, end_date):
    """保存结果到CSV"""
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_本地数据_{start_date}到{end_date}_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"\n📁 已保存: {filename}")
        print(f"📊 记录数: {len(df)}")
        print(f"📅 日期范围: {df['日期'].min()} - {df['日期'].max()}")
        print(f"📈 交易日: {df['日期'].nunique()} 天")
        
        # 显示前几条数据预览
        print(f"\n📋 数据预览:")
        for i, row in df.head(3).iterrows():
            print(f"   {row['日期']} {row['目标时间']}: {row['收盘价']:.2f}元")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

# 快速使用指南
"""
🚀 快速使用步骤：

1️⃣ 确保数据已下载：
   - QMT -> 操作 -> 数据管理
   - 股票：000651.SZ
   - 周期：1分钟
   - 时间：20250430-20250731

2️⃣ 运行此策略：
   - 复制代码到QMT策略编辑器
   - 点击运行

3️⃣ 获得结果：
   - 自动生成CSV文件
   - 包含9:35、9:40、9:45三个时间点的股价数据

📝 输出CSV包含字段：
   - 股票代码、股票名称、日期、目标时间、实际时间
   - 开盘价、最高价、最低价、收盘价、成交量、成交额

⚡ 优势：
   - 使用本地数据，速度极快
   - 无需网络连接
   - 数据完整准确
"""
