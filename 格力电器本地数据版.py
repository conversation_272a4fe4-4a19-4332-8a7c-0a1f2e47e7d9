# coding:gbk
"""
格力电器本地数据版
专门使用get_local_data从本地获取已下载的数据并保存为CSV
适用于已下载20250430-20250731数据的情况
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("🏠 格力电器本地数据版启动")
    print("📅 使用本地已下载的2025年4月30日到7月31日数据")
    print("🎯 获取 9:35、9:40、9:45 三个时间点股价")
    print("💾 数据将自动保存为CSV文件")

def handlebar(ContextInfo):
    """主执行函数 - 使用本地数据"""
    
    stock_code = "000651.SZ"  # 格力电器
    start_date = "20250430"
    end_date = "20250731"
    
    print(f"\n🏠 开始从本地获取数据...")
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 数据范围: {start_date} 到 {end_date}")
    
    try:
        # 使用get_local_data从本地获取数据
        print("📡 正在从本地读取分钟数据...")
        
        local_data = ContextInfo.get_local_data(
            field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if stock_code in local_data and not local_data[stock_code].empty:
            df = local_data[stock_code]
            print(f"✅ 成功从本地获取 {len(df):,} 条分钟数据")
            
            # 显示数据时间范围
            first_date = df.index[0][:8]
            last_date = df.index[-1][:8]
            print(f"📅 数据时间范围: {first_date} 到 {last_date}")
            
            # 处理数据并提取目标时间点
            all_target_data = process_local_data(df, stock_code)
            
            # 保存所有目标时间点数据
            if all_target_data:
                save_all_target_data_csv(all_target_data, start_date, end_date)
                print(f"🎯 共提取 {len(all_target_data)} 个目标时间点数据")
            else:
                print("❌ 未找到任何目标时间点数据")
                
        else:
            print("❌ 本地数据为空或不存在")
            print("💡 请确保已通过QMT数据管理下载了相应时间段的分钟数据")
            print("💡 下载路径: 操作 -> 数据管理 -> 选择股票和时间范围下载")
            
    except Exception as e:
        print(f"❌ 获取本地数据出错: {str(e)}")
        print("💡 可能的解决方案:")
        print("1. 确保已下载相应时间段的历史数据")
        print("2. 检查股票代码是否正确")
        print("3. 确认数据下载完整")

def process_local_data(df, stock_code):
    """处理本地数据，提取目标时间点"""
    
    target_times = ["0935", "0940", "0945"]  # 目标时间点
    all_target_data = []
    
    # 按日期分组处理
    dates_processed = set()
    
    print(f"\n📊 开始处理各交易日数据...")
    print("=" * 70)
    
    for index, row in df.iterrows():
        # 提取日期 (YYYYMMDD格式)
        date_str = index[:8]
        
        # 避免重复处理同一天
        if date_str in dates_processed:
            continue
            
        # 获取当天的所有数据
        day_data = df[df.index.str.startswith(date_str)]
        
        if not day_data.empty:
            print(f"📅 处理 {date_str}: {len(day_data)} 条数据")
            
            # 提取当天的目标时间点
            day_target_data = extract_target_times_from_day(day_data, date_str, target_times, stock_code)
            all_target_data.extend(day_target_data)
            
            dates_processed.add(date_str)
    
    return all_target_data

def extract_target_times_from_day(day_data, date_str, target_times, stock_code):
    """从单日数据中提取目标时间点"""
    
    day_target_data = []
    
    for target_time in target_times:
        # 查找匹配的时间数据
        matching_data = find_closest_time_data(day_data, target_time)
        
        if matching_data is not None:
            target_data = {
                '股票代码': stock_code,
                '股票名称': '格力电器',
                '日期': date_str,
                '目标时间': target_time,
                '实际时间': matching_data.name,
                '开盘价': round(matching_data['open'], 2),
                '最高价': round(matching_data['high'], 2),
                '最低价': round(matching_data['low'], 2),
                '收盘价': round(matching_data['close'], 2),
                '成交量': int(matching_data['volume']),
                '成交额': round(matching_data['amount'], 2)
            }
            day_target_data.append(target_data)
            print(f"  ✅ {target_time}: 收盘价 {target_data['收盘价']:.2f} 元")
        else:
            print(f"  ❌ {target_time}: 未找到数据")
    
    return day_target_data

def find_closest_time_data(day_data, target_time):
    """查找最接近目标时间的数据"""
    
    # 尝试多种匹配模式
    patterns = [
        target_time + "00",  # 0935 -> 093500
        target_time + "30",  # 0935 -> 093530
        target_time,         # 0935
        target_time[:3]      # 0935 -> 093 (匹配093X)
    ]
    
    for pattern in patterns:
        # 查找包含该模式的时间
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]  # 返回第一个匹配的数据
    
    return None

def save_all_target_data_csv(all_target_data, start_date, end_date):
    """保存所有目标时间点数据到CSV"""
    try:
        # 创建DataFrame
        df_save = pd.DataFrame(all_target_data)
        
        # 按日期和时间排序
        df_save = df_save.sort_values(['日期', '目标时间'])
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_本地数据_{start_date}到{end_date}_目标时间点_{timestamp}.csv"
        
        # 保存到CSV文件
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"\n📁 数据已保存到: {filename}")
        print(f"📊 共保存 {len(df_save)} 条记录")
        
        # 显示数据概览
        print(f"\n📈 数据概览:")
        print(f"   日期范围: {df_save['日期'].min()} 到 {df_save['日期'].max()}")
        print(f"   交易日数: {df_save['日期'].nunique()} 天")
        print(f"   时间点数: {len(df_save)} 个")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存CSV文件失败: {str(e)}")
        return None

# 使用说明
"""
本地数据版使用说明：

1. 数据准备：
   - 在QMT中打开"操作" -> "数据管理"
   - 选择股票：000651.SZ (格力电器)
   - 选择周期：1分钟
   - 选择时间范围：20250430 到 20250731
   - 点击下载数据

2. 运行策略：
   - 将此代码复制到QMT策略编辑器
   - 点击运行即可从本地获取数据

3. 输出文件：
   - 自动生成CSV文件，包含所有目标时间点的股价数据
   - 文件名格式：格力电器_本地数据_起始日期到结束日期_目标时间点_时间戳.csv

4. 优势：
   - 速度快：直接从本地读取，无需网络请求
   - 稳定：不受网络波动影响
   - 完整：使用已下载的完整历史数据

5. get_local_data参数说明：
   - field_list: 需要获取的字段列表
   - stock_list: 股票代码列表
   - period: 数据周期 ('1m'表示1分钟)
   - start_time/end_time: 起止时间
   - count: 数据条数 (-1表示获取全部)
   - dividend_type: 复权类型 ('none'表示不复权)
   - fill_data: 是否填充数据

6. 注意事项：
   - 确保已通过QMT数据管理下载了相应的历史数据
   - 本地数据的完整性直接影响结果的准确性
   - 如果某些时间点没有数据，可能是非交易时间或停牌
"""
