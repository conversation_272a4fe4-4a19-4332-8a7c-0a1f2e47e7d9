# coding:gbk
"""
格力电器直接执行版
解决策略不执行的问题，直接在init中完成所有操作
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数 - 直接执行所有操作"""
    
    print("=" * 60)
    print("🚀 格力电器直接执行版")
    print("📅 获取本地2025年4月30日-7月31日数据")
    print("🎯 提取9:35、9:40、9:45时间点股价")
    print("=" * 60)
    
    # 参数设置
    stock_code = "000651.SZ"
    start_date = "20250430"
    end_date = "20250731"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📊 任务参数:")
    print(f"   股票代码: {stock_code}")
    print(f"   股票名称: 格力电器")
    print(f"   时间范围: {start_date} - {end_date}")
    print(f"   目标时间: {', '.join(target_times)}")
    
    # 开始执行
    print(f"\n🔄 开始执行...")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 步骤1：获取本地数据
        print(f"\n📡 步骤1: 获取本地数据")
        print(f"正在调用 get_local_data...")
        
        data = ContextInfo.get_local_data(
            field_list=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        print(f"✅ get_local_data 调用完成")
        
        # 步骤2：检查数据
        print(f"\n🔍 步骤2: 检查数据")
        
        if not data:
            print("❌ 返回的数据为空")
            print("💡 可能原因: 本地没有下载相应的历史数据")
            show_download_guide()
            return
        
        if stock_code not in data:
            print(f"❌ 数据中没有找到股票 {stock_code}")
            print(f"📋 返回的股票列表: {list(data.keys())}")
            return
        
        df = data[stock_code]
        
        if df.empty:
            print(f"❌ 股票 {stock_code} 的数据为空")
            show_download_guide()
            return
        
        print(f"✅ 数据检查通过")
        print(f"📊 获取到 {len(df):,} 条分钟数据")
        
        # 显示数据范围
        first_time = df.index[0]
        last_time = df.index[-1]
        first_date = first_time[:8]
        last_date = last_time[:8]
        
        print(f"📅 数据时间范围:")
        print(f"   开始: {first_time}")
        print(f"   结束: {last_time}")
        print(f"   日期范围: {first_date} - {last_date}")
        
        # 步骤3：处理数据
        print(f"\n⚙️ 步骤3: 处理目标时间点")
        
        results = []
        
        # 获取所有日期
        all_dates = set()
        for index in df.index:
            all_dates.add(index[:8])
        
        sorted_dates = sorted(all_dates)
        print(f"📅 发现 {len(sorted_dates)} 个交易日")
        
        # 处理每个交易日
        for i, date_str in enumerate(sorted_dates, 1):
            print(f"\n📅 处理第 {i}/{len(sorted_dates)} 天: {date_str}")
            
            # 获取当日数据
            day_data = df[df.index.str.startswith(date_str)]
            print(f"   当日数据: {len(day_data)} 条")
            
            # 处理每个目标时间
            day_results = []
            for target_time in target_times:
                found_data = find_time_data(day_data, target_time)
                
                if found_data is not None:
                    result = {
                        '股票代码': stock_code,
                        '股票名称': '格力电器',
                        '日期': date_str,
                        '目标时间': target_time,
                        '实际时间': found_data.name,
                        '开盘价': round(found_data['open'], 2),
                        '最高价': round(found_data['high'], 2),
                        '最低价': round(found_data['low'], 2),
                        '收盘价': round(found_data['close'], 2),
                        '成交量': int(found_data['volume']),
                        '成交额': round(found_data['amount'], 2)
                    }
                    results.append(result)
                    day_results.append(f"{target_time}:{result['收盘价']:.2f}")
                    print(f"   ✅ {target_time}: {result['收盘价']:.2f} 元")
                else:
                    print(f"   ❌ {target_time}: 未找到数据")
            
            if day_results:
                print(f"   📊 当日结果: {', '.join(day_results)}")
        
        # 步骤4：保存结果
        print(f"\n💾 步骤4: 保存结果")
        
        if not results:
            print("❌ 没有提取到任何数据")
            return
        
        # 保存到CSV
        df_result = pd.DataFrame(results)
        df_result = df_result.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_直接执行_{start_date}到{end_date}_{timestamp}.csv"
        
        df_result.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"✅ 保存成功!")
        print(f"📁 文件名: {filename}")
        print(f"📊 总记录: {len(df_result)} 条")
        print(f"📅 交易日: {df_result['日期'].nunique()} 天")
        print(f"⏰ 时间点: {len(df_result['目标时间'].unique())} 个")
        
        # 显示统计信息
        if '收盘价' in df_result.columns:
            min_price = df_result['收盘价'].min()
            max_price = df_result['收盘价'].max()
            avg_price = df_result['收盘价'].mean()
            print(f"💰 价格统计:")
            print(f"   最低价: {min_price:.2f} 元")
            print(f"   最高价: {max_price:.2f} 元")
            print(f"   平均价: {avg_price:.2f} 元")
        
        print(f"\n🎉 任务完成!")
        
    except Exception as e:
        print(f"\n❌ 执行出错: {str(e)}")
        print(f"📋 错误类型: {type(e).__name__}")
        
        # 提供解决建议
        print(f"\n💡 可能的解决方案:")
        print(f"1. 检查是否已下载本地数据")
        print(f"2. 确认股票代码是否正确")
        print(f"3. 检查时间范围是否有效")
        print(f"4. 重新下载历史数据")

def handlebar(ContextInfo):
    """K线处理函数 - 空函数，所有操作在init中完成"""
    pass

def find_time_data(day_data, target_time):
    """查找指定时间的数据"""
    
    # 尝试多种匹配模式
    patterns = [
        target_time + "00",  # 0935 -> 093500
        target_time + "30",  # 0935 -> 093530
        target_time,         # 0935
        target_time[:3]      # 0935 -> 093
    ]
    
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def show_download_guide():
    """显示数据下载指南"""
    print(f"\n📖 数据下载指南:")
    print(f"1. 打开QMT客户端")
    print(f"2. 点击菜单: 操作 -> 数据管理")
    print(f"3. 在数据管理界面:")
    print(f"   - 市场: 深圳A股")
    print(f"   - 股票: 000651 (格力电器)")
    print(f"   - 周期: 1分钟")
    print(f"   - 开始时间: 2025-04-30")
    print(f"   - 结束时间: 2025-07-31")
    print(f"4. 点击下载数据")
    print(f"5. 等待下载完成后重新运行策略")

# 使用说明
"""
🚀 直接执行版特点:

✅ 解决问题:
- 不依赖K线数据，直接在init中执行
- 避免等待handlebar调用
- 立即显示执行结果
- 详细的错误诊断

🎯 执行流程:
1. 在init函数中直接执行所有操作
2. 详细的步骤提示和进度显示
3. 完整的错误处理和解决建议
4. 立即保存结果到CSV

💡 如果仍然没有输出:
1. 检查QMT是否正常连接
2. 确认策略编译成功
3. 查看是否有其他错误提示
4. 尝试重新启动QMT
"""
