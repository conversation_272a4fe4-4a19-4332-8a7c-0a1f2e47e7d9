# coding:gbk
"""
格力电器最终版
使用正确的get_history_data API调用方式
获取9:35、9:40、9:45时间点的5分钟K线数据并保存为CSV
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化函数 - 执行主要任务"""
    
    print("=" * 70)
    print("🎉 格力电器最终版")
    print("✅ 使用正确的API调用方式")
    print("📊 获取5分钟K线数据")
    print("🎯 目标时间: 9:35、9:40、9:45")
    print("=" * 70)
    
    # 执行主要任务
    execute_final_task(ContextInfo)

def execute_final_task(ContextInfo):
    """执行最终任务"""
    
    # 参数设置
    stock_code = "000651.SZ"
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n📋 任务参数:")
    print(f"   股票: {stock_code} (格力电器)")
    print(f"   周期: 5分钟K线")
    print(f"   目标: {', '.join(target_times)}")
    print(f"   方法: get_history_data (3个参数)")
    
    try:
        print(f"\n🌐 开始获取历史数据...")
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 使用正确的API调用方式（3个参数：股票代码、周期、字段）
        data = ContextInfo.get_history_data(stock_code, '5m', ['open', 'high', 'low', 'close', 'volume', 'amount'])
        
        print(f"✅ 数据获取成功")
        
        # 检查数据
        if data is None or data.empty:
            print(f"❌ 获取的数据为空")
            return
        
        print(f"📊 获取到 {len(data):,} 条5分钟K线数据")
        
        # 显示数据概况
        first_time = data.index[0]
        last_time = data.index[-1]
        print(f"📅 数据范围: {first_time} 到 {last_time}")
        print(f"📋 数据字段: {list(data.columns)}")
        
        # 过滤到指定时间范围的数据
        filtered_data = filter_date_range(data, "20250430", "20250731")
        
        if filtered_data.empty:
            print(f"❌ 指定时间范围内无数据")
            return
        
        print(f"📅 过滤后数据: {len(filtered_data):,} 条 (2025年4月30日-7月31日)")
        
        # 处理目标时间点
        results = process_target_times(filtered_data, stock_code, target_times)
        
        if results:
            # 保存结果
            filename = save_final_results(results)
            print(f"\n🎉 任务完成!")
            print(f"📁 文件: {filename}")
            print(f"📊 记录: {len(results)} 条")
            
            # 显示部分结果
            show_sample_results(results)
        else:
            print(f"❌ 未找到任何目标时间点数据")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")
        print(f"💡 请检查:")
        print(f"   1. QMT客户端是否正常运行")
        print(f"   2. 网络连接是否正常")
        print(f"   3. 股票代码是否正确")

def filter_date_range(data, start_date, end_date):
    """过滤指定日期范围的数据"""
    
    try:
        # 过滤日期范围
        filtered_data = data[
            (data.index >= start_date) & 
            (data.index <= end_date + "235959")
        ]
        return filtered_data
    except:
        # 如果过滤失败，返回原数据
        return data

def process_target_times(data, stock_code, target_times):
    """处理目标时间点数据"""
    
    results = []
    
    # 获取所有交易日
    all_dates = set()
    for index in data.index:
        all_dates.add(index[:8])
    
    sorted_dates = sorted(all_dates)
    print(f"\n📅 处理 {len(sorted_dates)} 个交易日:")
    
    for i, date_str in enumerate(sorted_dates, 1):
        # 获取当日5分钟数据
        day_data = data[data.index.str.startswith(date_str)]
        
        if day_data.empty:
            continue
        
        print(f"  {i:2d}. {date_str}: {len(day_data)} 条5分钟K线", end=" -> ")
        
        day_results = []
        for target_time in target_times:
            found_data = find_target_time_data(day_data, target_time)
            
            if found_data is not None:
                result = {
                    '股票代码': stock_code,
                    '股票名称': '格力电器',
                    '日期': date_str,
                    '目标时间': target_time,
                    '实际时间': found_data.name,
                    '开盘价': round(found_data['open'], 2),
                    '最高价': round(found_data['high'], 2),
                    '最低价': round(found_data['low'], 2),
                    '收盘价': round(found_data['close'], 2),
                    '成交量': int(found_data['volume']),
                    '成交额': round(found_data['amount'], 2)
                }
                results.append(result)
                day_results.append(f"{target_time}:{result['收盘价']:.2f}")
        
        if day_results:
            print(f"✅ {', '.join(day_results)}")
        else:
            print(f"❌ 无数据")
    
    return results

def find_target_time_data(day_data, target_time):
    """查找目标时间的5分钟K线数据"""
    
    # 5分钟K线时间匹配策略
    if target_time == "0935":
        # 9:35的价格在9:35的5分钟K线中，如果没有则查找9:30
        patterns = ["0935", "0930"]
    elif target_time == "0940":
        # 9:40的价格在9:40的5分钟K线中
        patterns = ["0940"]
    elif target_time == "0945":
        # 9:45的价格在9:45的5分钟K线中
        patterns = ["0945"]
    else:
        patterns = [target_time]
    
    # 按优先级查找
    for pattern in patterns:
        matches = day_data[day_data.index.str.contains(pattern)]
        if not matches.empty:
            return matches.iloc[0]
    
    return None

def save_final_results(results):
    """保存最终结果"""
    
    try:
        df = pd.DataFrame(results)
        df = df.sort_values(['日期', '目标时间'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_最终版_{timestamp}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 显示统计
        print(f"\n💾 保存统计:")
        print(f"   文件名: {filename}")
        print(f"   总记录: {len(df)} 条")
        print(f"   交易日: {df['日期'].nunique()} 天")
        print(f"   时间点: {len(df['目标时间'].unique())} 个")
        
        if '收盘价' in df.columns:
            min_price = df['收盘价'].min()
            max_price = df['收盘价'].max()
            avg_price = df['收盘价'].mean()
            print(f"   价格范围: {min_price:.2f} - {max_price:.2f} 元")
            print(f"   平均价格: {avg_price:.2f} 元")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return None

def show_sample_results(results):
    """显示部分结果样例"""
    
    print(f"\n📋 结果样例 (前5条):")
    print("-" * 60)
    
    for i, result in enumerate(results[:5], 1):
        print(f"{i}. {result['日期']} {result['目标时间']}: {result['收盘价']:.2f}元")
    
    if len(results) > 5:
        print(f"   ... (共{len(results)}条记录)")

def handlebar(ContextInfo):
    """K线处理函数"""
    pass

# 使用说明
"""
🎉 最终版本说明:

✅ 解决的问题:
- 使用正确的API调用方式: get_history_data(stock_code, period, fields)
- 需要3个参数：股票代码、周期、字段列表
- 获取完整的历史数据，然后进行时间过滤

🎯 功能特点:
- 自动获取格力电器的5分钟K线数据
- 提取9:35、9:40、9:45三个时间点的股价
- 智能匹配5分钟K线时间
- 自动保存为CSV文件

📊 数据说明:
- 使用5分钟K线数据，精度完全够用
- 自动过滤到2025年4月30日-7月31日范围
- 包含完整的OHLC数据和成交量信息

💡 使用方法:
1. 确保QMT客户端正常运行
2. 运行此策略
3. 等待执行完成
4. 查看生成的CSV文件

🔧 技术要点:
- API调用: ContextInfo.get_history_data(stock_code, '5m')
- 时间匹配: 智能匹配5分钟K线时间点
- 数据过滤: 自动过滤指定日期范围
- 错误处理: 完整的异常处理机制
"""
